const path = require('path')

module.exports = {
  // 基础路径
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',

  // 输出目录
  outputDir: 'unpackage/dist/build/web',

  // 静态资源目录
  assetsDir: 'static',

  // 开发服务器配置
  devServer: {
    host: '0.0.0.0', // 允许外部访问
    port: 8080, // 端口号
    open: true, // 自动打开浏览器
    hot: true, // 热重载
    disableHostCheck: true, // 关闭 Host 检查
    historyApiFallback: true, // 支持 history 路由
    overlay: {
      warnings: false,
      errors: true
    },
    // 代理配置 - 可根据需要配置API代理
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },

  // 生产环境配置
  configureWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      // 生产环境优化
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial'
            },
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: 5,
              chunks: 'initial'
            }
          }
        }
      }
    }
  },

  // CSS 配置
  css: {
    extract: process.env.NODE_ENV === 'production',
    sourceMap: process.env.NODE_ENV !== 'production'
  },

  // 链式配置
  chainWebpack: config => {
    // 设置别名
    config.resolve.alias
      .set('@', path.resolve(__dirname, '.'))
      .set('~', path.resolve(__dirname, './'))

    // 优化图片
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 8192,
        name: 'static/img/[name].[hash:8].[ext]'
      })
  }
};