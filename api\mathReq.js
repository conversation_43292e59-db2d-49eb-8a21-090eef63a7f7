//如果用uni请求
import http from '@/common/uni.interface.js';
//如果是用uview请求,就用下面这句
//const http = uni.$u.http

//获取数学试卷
export function getTestPaper(params) {
  return http.get('dsx/math/wap/mathTestPaper/getTestPaperDetails', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}

//提交题目
export function sendTestPaper(params) {
  return http.post('dsx/math/wap/mathAnswerQuestion/singleSubmission', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}

//学生交卷
export function studentSubmit(params) {
  return http.post('dsx/math/wap/mathStudentPauseNode/studentSubmit', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}

//获取对战结果
export function getTestPaperPKResult(params) {
  return http.get('dsx/math/wap/mathStudentGrouping/getGroupingStudentDetails', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}

//获取完整token
export function getToken(params) {
  return http.get('dsx/math/wap/token', params)
}

//答题解析按钮中 试卷列表
export function getStudentExamResults(params) {
  return http.get('dsx/math/wap/mathStudentPauseNode/getStudentExamResults', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}
export function getTestPaperWrongDetails(params) {
  return http.get('dsx/math/wap/mathTestPaper/getTestPaperWrongDetails', params,{ header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}
// 获取题目列表
export function getQuestionList(data) {
  return http.post('dsx/math/wap/wrongBook/queryWrongBookPrintDetail', data , { header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },})
}

