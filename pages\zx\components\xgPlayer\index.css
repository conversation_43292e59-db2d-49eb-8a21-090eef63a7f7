/* 进度条字体大小 */
/* .xgplayer.xgplayer-mobile .xgplayer-time {
  min-width: 30px;
  font-size: 20px;
}
.xgplayer .btn-text {
  font-size: 19px;
} */
/*  弹幕字体大小 */
/* .xgplayer xg-trigger .time-preview{
  font-size: 22px;
} */
 .xgplayer .xg-top-bar{
    width: 100%;
 }
/*  进度条高度 */
.xgplayer-progress {
  .xgplayer-progress-outer {
    height: 0.5vw;
    min-height: 2px;
  }

  .xgplayer-spot {
    aspect-ratio: 1;
    height: auto;
  }
}

/*  进度条按钮颜色 */
.xgplayer .xgplayer-progress .xgplayer-progress-btn {
  background: rgb(94 255 209 / 30%) !important;
  border: .5px solid rgba(255, 94, 94, .056545) !important;
  box-shadow: 0 0 1px #00ff8762 !important;
  /* background-color: transparent;
    border-width: 0px;
    box-shadow: none; */
}

/* .xgplayer-progress:hover {
  .xgplayer-progress-outer{
    height: 7px;
  }
  .xgplayer-spot{
    height: 8.5px;
  } 
}   */
/* 防止换行 */
.xgplayer .xg-right-grid {
  flex-wrap: nowrap;
}

/*  节点高度 */
/* .xgplayer-mobile .xgplayer-spot{
  height: 6px;
} */
.xgplayer video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  outline: none;
}

/* 列表高光颜色 */
.xgplayer .xg-options-list li:hover,
.xgplayer .xg-options-list li.selected {
  color: #42B983;
  opacity: 1;
}

/*  错误提示 */
.xgplayer-error .xgplayer-error-refresh {
  color: #42B983;
}

/* .xgplayer :first-child {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    outline: none;
} */
.xgplayer .video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  outline: none;
}

/* top-plugin 插件 */
.top-plugin {
  color: #fff;
  position: absolute;
  top: 17px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.5vw;
}

/* story-nodes 插件 */
.story-nodes-item {
  /* font-size: 22px;
  color: #42B983;
  text-shadow: 1px 1px 1px #fff;
  position: absolute;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%); */

  font-size: 14px;
  padding: 6px 8px;
  border-radius: 8px;
  background: #00000088;
  color: #fff;
  position: absolute;
  bottom: 15%;
  left: 50%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  word-break: break-all;
  /* 强制换行
  /* white-space: nowrap; */
}