import { Plugin, Events } from 'xgplayer'

const { POSITIONS } = Plugin

// demoPlugin.js
export default class demoPlugin extends Plugin {

  timer = null;
  currentTime = '00:00:00';
  // 插件的名称，将作为插件实例的唯一key值
  static get pluginName() {
    return 'topPlugin'
  }

  static get defaultConfig() {
    return {
      position: POSITIONS.ROOT_TOP
    }
  }

  constructor(args) {
    super(args)
  }

  beforePlayerInit() {
    // TODO 播放器调用start初始化播放源之前的逻辑
  }

  afterPlayerInit() {
    // TODO 播放器调用start初始化播放源之后的逻辑
  }

  afterCreate() {
    //TODO 插件实例化之后的一些逻辑

    // // 更新时间
    // this.on(Events.PLAY, () => {
    //   console.log('play', this.player.currentTime)
    //   this.timer = setInterval(() => {
    //     this.update(this.player.currentTime + 1)
    //   }, 1000)
    // })
    // this.on(Events.PAUSE, () => {
    //   clearInterval(this.timer)
    //   this.timer = null
    // })
    // const progress = this.player.getPlugin('progress')

    // progress?.addCallBack('dragend', (data) => {
    //   this.update((data.currentTime))
    //   this.timer = setInterval(() => {
    //     this.update(this.player.currentTime + 1)
    //   }, 1000)
    // })
    // progress?.addCallBack('dragstart', (data) => {
    //   this.update((data.currentTime))
    //   clearInterval(this.timer)
    //   this.timer = null
    // })
    // progress?.addCallBack('dragmove', (data) => {
    //   this.update((data.currentTime))
    // })
    // progress?.addCallBack('click', (data) => {
    //   this.update((data.currentTime))
    // })
  }

  // 秒数转换成时分秒格式
  formatTime(time) {
    let hour = Math.floor(time / 3600)
    time = time % 3600
    let minute = Math.floor(time / 60)
    let second = Math.round(time % 60)
    return `${hour}:${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`
  }
  // 更新时间
  update(time) {
    this.currentTime = this.formatTime(time)
    this.setHtml(`<div class="top-plugin">${this.currentTime}</div>`, () => {
      // console.log('dom重置完成')
    })
  }

  destroy() {
    this.unbind('.icon', 'click', this.onIconClick)
    this.unbind('click', this.onClick)
    this.icon = null
    // 播放器销毁的时候一些逻辑
  }

  render() {
    return `<div class="top-plugin">00:00</div>`
  }
}