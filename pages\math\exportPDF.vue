<template>
  <view class="" v-if="!loading">
    <!-- <button @click="downloadPDF" style="margin: 20px 0">生成PDF</button> -->

    <!-- 打印标签 -->
    <view class="pdf-container" id="pdf-details">
      <!-- 题头 -->
      <view class="pdf-header">
        <view class="pdf-header-code">
          <img :src="qrCodeUrl" alt="" />
          <span>扫码查看解析</span>
        </view>
      </view>
      <!-- 题目 -->
      <view class="question-wrapper">
        <view class="subjectTest page-break-avoid" v-for="(data1, index) in dataList" :key="index">
          <view v-if="data1">
            <view class="title topic-details">
              {{ index + 1 }}.【{{ getTypeName(data1.questionType) }}】
              <view style="height: auto;overflow: visible;">
                <span v-for="(segment, index) in parseContent(data1.questionText)" :key="index"
                  style="display: inline;  vertical-align: top">
                  <span v-if="segment.type === 'text'" style="display: inline;  vertical-align: top">{{
                    segment.content }}</span>
                  <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                </span>
              </view>
            </view>
            <view class="imgs" v-if="data1.questionFileUrl">
              <image :src="img" style="max-width: 100%; max-height: 100rpx" mode="heightFix" @click="previewImage(img,data1.questionFileUrl.split(','))"
                v-for="(img, imgIndex) in data1.questionFileUrl.split(',')" :key="imgIndex"></image>
            </view>
            <!-- 解析 -->
            <view class="questions" v-for="(obj, qIndex) in data1.questionOptions" :key="qIndex">
              <view class="questionsTitle" v-if="data1.questionOptions.length > 1">
                <view style="">
                  {{
                    data1.questionType == 1
                      ? getTypeName(data1.questionType) + (qIndex + 1)
                      : "问" + (qIndex + 1)
                  }}:
                  <view v-for="(segment, index) in parseContent(obj.question)" :key="index"
                    style="display: inline;  vertical-align: top">
                    <span v-if="segment.type === 'text'" style="display: inline;  vertical-align: top">{{
                      segment.content }}</span>
                    <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                  </view>
                </view>
              </view>
              <view class="questionsItem" v-for="(e, i) in obj.optionList" :key="i">
                <view style="line-height: 35rpx">
                  <span style="
                      display: inline;
                       vertical-align: top;
                      margin-right: 10rpx;
                    ">{{ e.choiceOption }}.</span>
                  <span v-for="(segment, index) in parseContent(e.content)" :key="index"
                    style="display: inline;  vertical-align: top">
                    <span v-if="segment.type == 'text'" style="display: inline;  vertical-align: top">{{
                      segment.content }}</span>
                    <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                  </span>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 解析 -->
      <view class="pdf-split-page" v-if="printMode == 2">
        <h3 class="parse-title">答案与解析</h3>
        <view class="question-wrapper page-break-avoid" v-for="(data1, index) in dataList" :key="index">
          <view class="question-title">
            <view class="title topic-details">
              {{ index + 1 }}.【{{ getTypeName(data1.questionType) }}】
              <view style="">
                <span v-for="(segment, index) in parseContent(data1.questionText)" :key="index"
                  style="display: inline;  vertical-align: top">
                  <span v-if="segment.type === 'text'" style="display: inline;  vertical-align: top">{{
                    segment.content }}</span>
                  <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                </span>
              </view>
            </view>
            <!-- <view class="question-answer">
              <view class="question-answer-title">答案：</view>
              <view><span class="right-answer-text">{{ data1.correctAnswer }}</span> </view>
              <view class="question-answer-title">解析：</view>
              <view class="analysisParent">
                <view style="padding: 30rpx 0rpx; page-break-inside: avoid !important">
                  <view class="imgs" v-if="data1.analysisImgList && data1.analysisImgList.length">
                    <image v-for="(img, imgIndex) in data1.analysisImgList" :src="img" :key="imgIndex"
                      style="width: 200rpx; height: 200rpx" @click="previewImage(img)"></image>
                  </view>
                  <view style="">
                    <view v-for="(segment, index) in parseContent(data1.analysis)" :key="index"
                      style="display: inline;  vertical-align: top">
                      <span v-if="segment.type === 'text'" style="display: inline;  vertical-align: top">{{
                        segment.content }}</span>
                      <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                    </view>
                  </view>
                </view>
              </view>
            </view> -->
            <view class="questionsTitle" style="margin-top: 10rpx">
              正确答案：
              <span class="questionsRight">{{ data1.correctAnswer }}</span>
            </view>
            <view class="analysisParent">
              <view class="analysisTitle" @click="toggleAnalysis(data1)">
                <view style="color: #555555">本题解析</view>
              </view>
              <view style="padding: 10rpx 0rpx; page-break-inside: avoid !important">
                <view class="imgs" v-if="data1.analysisImgList && data1.analysisImgList.length">
                  <image :src="img" style="max-width: 100%; max-height: 100rpx" mode="heightFix"
                    @click="previewImage(img,data1.analysisImgList)" v-for="(img, imgIndex) in data1.analysisImgList" :key="imgIndex"></image>
                </view>
                <view style="">
                  <view v-for="(segment, index) in parseContent(data1.analysis)" :key="index"
                    style="display: inline;  vertical-align: top">
                    <span v-if="segment.type === 'text'" style="display: inline;  vertical-align: top">{{
                      segment.content }}</span>
                    <span v-else class=" pdf-math-span" v-html="renderFormula(segment.content)"></span>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 页头页脚 -->
    <!-- 页头 -->
    <view class="pdf-header pdf-header-top">
      <view class="pdf-header-title">
        <view>数学超人 错题本</view>
        <view>鼎校教育</view>
      </view>
    </view>
    <!-- 页脚 -->
    <div class="pdf-footer" style="
          font-weight: bold;
          padding: 15px 8px;
          width: 100%;
          border-top: 1px solid rgba(0, 0, 0, 0.85);
          position: fixed;
          top: -100vh;
        ">
      <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 5px;
          ">
        我是页尾
      </div>
      <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
          ">
        第
        <div class="pdf-footer-page"></div>
        页 / 第
        <div class="pdf-footer-page-count"></div>
        页
      </div>
    </div>
  </view>
</template>

<script>
import { PdfLoader } from "./utils/PdfLoader";
import { getQuestionList } from "../../api/mathReq";
export default {
  data() {
    return {
      // 模式
      printMode: 1, // 1打印 题干; 2打印 题干+解析
      loading: true,
      data1: {}, // 你的题目数据
      status: 2, // 状态
      questionImages: [], // 题目图片
      answerLists: [], // 答案列表
      typeArr: [
        { name: "单选题", id: 0 },
        { name: "填空题", id: 1 },
        { name: "计算题", id: 2 },
        { name: "解方程题", id: 3 },
        { name: "证明题", id: 4 },
        { name: "几何综合题", id: 5 },
      ],
      // data1: {
      //   id: "1398323087728852992",
      //   questionId: "1398323087401697280",
      //   questionBankId: "1382317454631780353",
      //   questionScore: 0,
      //   questionText:
      //     "计算 \\((2-1)(2+1)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right) \\ldots\\left(2^{32}+1\\right)\\) 的结果为（ ）计算 \\((2-1)(2+1)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right) \\ldots\\left(2^{32}+1\\right)\\) 的结果为（ ）",
      //   questionImage: [],
      //   myScore: 0,
      //   questionDifficulty: 2,
      //   syncStatus: "0",
      //   questionType: 0,
      //   correctAnswer: "B",
      //   myAnswer: "C",
      //   myAnswerImage: [],
      //   answeringStatus: 2,
      //   analysis:
      //     "本题考查了平方差公式的应用，注意：\\((a+b)(a-b)=a^{2}-b^{2}\\) ．依次运用平方差公式进行计算即可。解：\\((2-1)(2+1)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right) \\ldots\\left(2^{32}+1\\right)\\)\\(=\\left(2^{2}-1\\right)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{4}-1\\right)\\left(2^{4}+1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{8}-1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{16}-1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{32}-1\\right)\\left(2^{32}+1\\right)\\) ，\\(=2^{64}-1\\) ，故选：C．",
      //   analysisImg: "",
      //   analysisData:
      //     "本题考查了平方差公式的应用，注意：\\((a+b)(a-b)=a^{2}-b^{2}\\) ．依次运用平方差公式进行计算即可。解：\\((2-1)(2+1)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right) \\ldots\\left(2^{32}+1\\right)\\)\\(=\\left(2^{2}-1\\right)\\left(2^{2}+1\\right)\\left(2^{4}+1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{4}-1\\right)\\left(2^{4}+1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{8}-1\\right)\\left(2^{8}+1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{16}-1\\right)\\left(2^{16}+1\\right)\\left(2^{32}+1\\right)\\) ，\\(=\\left(2^{32}-1\\right)\\left(2^{32}+1\\right)\\) ，\\(=2^{64}-1\\) ，故选：C．",
      //   analysisImgList: null,
      //   createTime: "2025-07-25 15:16:44",
      //   mathSmallQuestionList: [
      //     {
      //       question: "",
      //       optionList: [
      //         { id: "1398323087724658692", content: "0 个", choiceOption: "A" },
      //         { id: "1398323087724658693", content: "1 个", choiceOption: "B" },
      //         { id: "1398323087724658694", content: "2 个", choiceOption: "C" },
      //         { id: "1398323087724658695", content: "3 个或更多", choiceOption: "D" },
      //       ],
      //     },
      //   ],
      //   serialNumber: 1,
      // },
      dataList: [], // 题目列表
      qrCodeUrl: "", // 二维码地址
    };
  },
  onLoad(e) {
    this.printMode = +e.printMode || 1;
    this.phone = e.phone || "";
    this.printId = e.printId || "";
    // let sendData = JSON.parse(decodeURIComponent(e.sendData || "{}"));
    // this.sendData = sendData || {};
    // console.log("%csendData : ", "color:#fff;background:#000", this.sendData);
    this.initQuestion();
  },
  mounted() {
    // this.downloadPDF();
  },
  methods: {
    getTypeName(typeId) {
      const type = this.typeArr.find((e) => e.id == typeId);
      return type ? type.name : "";
    },

    isPhotoType(typeId) {
      return typeId != 0 && typeId != 1;
    },

    toggleAnalysis(item) {
      item.isShowAnalysis = !item.isShowAnalysis;
      this.$forceUpdate();
    },

    checkAnswer(e, index) {
      if (this.status == 2) return;
      this.answerLists[index].answer = e.choiceOption;
      this.data1.mathSmallQuestionList[index].myAnswer = e.choiceOption;
      this.$forceUpdate();
      let answers = this.answerLists.map((e) => e.answer).join(",");
      // 如果需要触发事件可以在这里触发
      // this.$emit('checked', answers);
    },

    // 初始化
    async initQuestion() {
      this.loading = true;
      // ?printId=${this.printId}&studentMobilePhone=${this.phone}&wrongBookIdList=[]
      try {
        const res = await getQuestionList({
          printId: this.printId,
          studentMobilePhone: this.phone,
          wrongBookIdList: [],
        });
        // encode编码
        // const data = {
        //   phone: this.phone,
        //   printId: this.printId,
        // }
        // console.log('%cres : ', 'color:#fff;background:#000', res , encodeURIComponent(JSON.stringify(res)));
        // 解码
        // const res = this.sendData;
        if (res) {
          this.qrCodeUrl = res.qrCodeUrl;
          const dataList = res.mathWrongBookVoList || res.mathWrongPrintVoList || [];
          this.dataList = dataList.map((i) => {
            let questionOptions =
              typeof i.questionOptions == "string"
                ? JSON.parse(i.questionOptions)
                : i.questionOptions;
            i.questionOptions = questionOptions;
            i.showAnswers = false;
            return i;
          });
          this.loading = false;
          // 打印
          this.$nextTick(() => {
            setTimeout(() => {
              this.downloadPDF();
            }, 500);
          });
        }
      } catch (error) {
        console.log('%cerror : ', 'color:#fff;background:#000', error);
        this.loading = false;
        uni.showToast({
          title: "获取错题信息失败" + error,
          duration: 2000,
        });
      }
    },

    parseContent(text) {
      if (!text) return [];
      const regex = /\\\((.*?)\\\)/g;
      let lastIndex = 0;
      let match;
      const segments = [];

      while ((match = regex.exec(text)) !== null) {
        if (match.index > lastIndex) {
          segments.push({
            type: "text",
            content: text.substring(lastIndex, match.index),
          });
        }

        segments.push({
          type: "formula",
          content: match[1],
        });

        lastIndex = match.index + match[0].length;
      }

      if (lastIndex < text.length) {
        segments.push({
          type: "text",
          content: text.substring(lastIndex),
        });
      }
      return segments;
    },

    renderFormula(text) {
      try {
        if (!text) return "";
        const rendered = this.$katex.renderToString(text, {
          throwOnError: false,
          displayMode: false,
          strict: false,
          trust: true,
        });

        if (rendered.includes("ParseError")) {
          return text;
        }
        return rendered;
      } catch (error) {
        console.error("Formula rendering error:", error);
        return text;
      }
    },

    previewImage(currentUrl, imgList) {
      uni.previewImage({
        current: currentUrl,
        urls: imgList || [currentUrl],
      });
    },

    async downloadPDF() {
      // htmlPdf('数学超人错题本', document.querySelector('#pdf-details'));
      // return;
      const element = document.querySelector("#pdf-details");
      // const footer = document.querySelector(".pdf-footer");
      const header = document.querySelector(".pdf-header-top");
      const pdfObj = new PdfLoader(element, {
        fileName: "数学超人错题本.pdf",
        groupName: "page-break-avoid",
        splitName: "pdf-split-page",
        // splitName: 'page-break-avoid',
        isPageMessage: true,
        format: "a4",
        header,
      });
      pdfObj
        .getPdf()
        .then(async (res) => { })
        .catch((error) => { });
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  // .topic-details {
  // .katex .base {
  //   display: inline;
  //   white-space: normal;
  //   overflow: visible !important; /* 禁用裁剪 */
  // }

  // }
  .katex-html {
    overflow: visible !important;
    /* 禁用裁剪 */
  }
}

.pdf-math-span {
  max-width: 600rpx;
  overflow: auto;
  display: inline;
  vertical-align: top;
  overflow: visible !important;
}

.pdf-header-top {
  padding: 20rpx 40rpx 0;
  color: #666;
  font-size: 16rpx;
}

.pdf-header {
  color: #666;

  .pdf-header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pdf-header-code {
    // margin-top: 20rpx;
    display: flex;
    justify-content: start;
    align-items: end;

    img {
      width: 100rpx;
      height: 100rpx;
    }

    span {
      margin-left: 10rpx;
      color: #31cf93;
    }
  }
}

.pdf-container {
  padding: 0 20rpx 20rpx;
  font-size: 16rpx;
  color: #333;

  .subjectTest {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    margin-bottom: 15px;
    border-radius: 10rpx;
    background-color: #fff;
    margin-top: 10rpx;
    padding: 15rpx 0 0;

    .title {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15rpx;
    }
  }

  .imgs {
    margin-bottom: 15rpx;
    padding: 12rpx;
    justify-content: space-evenly;
    flex-wrap: wrap;
    display: flex;
  }

  .questions {
    .questionsItem {
      margin: 10rpx auto 9rpx;
      width: 650rpx;
      // line-height: 60rpx;
      padding-left: 15rpx;
      box-sizing: border-box;
      border: 1rpx solid #dfdfdf;
      border-radius: 10rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      color: #333333;

      &.right {
        border: 1rpx solid #94e6c7 !important;
        background: rgba(148, 230, 199, 0.15) !important;
        color: #31cf93 !important;
      }
    }
  }

  .questionsTitle {
    padding-left: 10rpx;
    min-height: 40rpx;
    line-height: 40rpx;
    background-color: #fff5ef;
    margin-top: 10rpx;
    color: #999;
  }

  .questionsRight {
    color: #009e74;
    font-weight: bold;
  }

  .analysisParent {
    border-radius: 10rpx;
    background-color: #f6f7f9;
    margin-top: 10rpx;
    padding: 10rpx 15rpx;
  }

  .analysisTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .page-break-before {
    padding: 20rpx;
  }

  .question-wrapper {
    margin-top: 20rpx;

    .question-answer {
      padding: 0 30rpx;

      .question-answer-title {
        padding: 20rpx 0;
      }

      .right-answer-text {
        color: #51cc92;
        font-weight: bold;
      }
    }
  }

  // /* 强制每个题目整体分页 */
  // .question-wrapper {
  //   page-break-inside: avoid !important;
  //   /* 打印时分页控制 */
  //   break-inside: avoid !important;
  //   margin-bottom: 20px;
  //   /* 题目间距 */
  // }

  // /* 打印时优化分页 */
  // @media print {
  //   .question-wrapper {
  //     page-break-inside: avoid;
  //   }

  //   body {
  //     overflow: visible !important;
  //   }
  // }
  .pdf-split-page {
    page-break-before: always;
    margin-top: 40px;
  }

  .page-break-avoid {
    page-break-before: always;
  }
}
</style>
