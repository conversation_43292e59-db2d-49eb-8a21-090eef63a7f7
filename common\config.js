// 环境判断 - 支持环境变量和传统方式
const IS_DEV = process.env.NODE_ENV === 'development' || process.env.NODE_ENV !== 'production'

// 从环境变量获取配置，如果没有则使用默认值
const getEnvConfig = () => {
  // API地址配置
  const DEV_URL = process.env.VUE_APP_API_BASE_URL || 'http://sit1.ngrok.dxznjy.com/'
  const PRO_URL = process.env.VUE_APP_API_BASE_URL || 'https://gateway.dxznjy.com/'

  // 企业微信配置
  const qyConfig = {
    corpid: process.env.VUE_APP_QY_CORPID || (IS_DEV ? 'wwcc79054d80e112df' : 'ww48ea1bc9b2477b69'),
    agentid: parseInt(process.env.VUE_APP_QY_AGENTID) || (IS_DEV ? 1000040 : 1000014),
    appid: process.env.VUE_APP_QY_APPID || (IS_DEV ? 'wx89164566cfdabfdf' : 'wxce2bd1113a024ff6')
  }

  return {
    DEV_URL,
    PRO_URL,
    qyConfig
  }
}

const envConfig = getEnvConfig()

module.exports = {
  // 是否开发模式
  IS_DEV,

  // API地址
  DEV_URL: envConfig.DEV_URL,
  PRO_URL: envConfig.PRO_URL,

  // 请求超时时间
  TIMEOUT: 60000,

  // 是否开启请求日志
  REQUEST_LOG: process.env.VUE_APP_REQUEST_LOG === 'true' || IS_DEV,

  // 是否开启请求异常提示
  CATCH_MESS: process.env.VUE_APP_DEBUG === 'true' || IS_DEV,

  // 请求时提示文字
  LOADING_TEXT: '正在加载',

  // 是否需要签名
  IS_SECRET: false,

  // 签名字符串
  APP_SECRET: 'uniapp!@#2022',

  // 跳转小程序相关信息
  qyConfig: envConfig.qyConfig,

  // 应用信息
  APP_TITLE: process.env.VUE_APP_TITLE || '鼎校智能教育',
  APP_VERSION: process.env.VUE_APP_VERSION || '1.0.0'
}
