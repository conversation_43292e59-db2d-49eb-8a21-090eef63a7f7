//如果用uni请求
import http from '@/common/uni.interface.js';

//如果是用uview请求,就用下面这句
//const http = uni.$u.http


//获取ticket
export function getTicket(params) {
  return http.get('scrm/qywechat/getTicket', params)
}
//绑定
export function bind(params) {
  return http.get('deliver/web/student/contact/info/bindMobileAndUnionId', params)
}
//获取二维码
export function getQrCode(params) {
  return http.get('deliver/web/student/contact/info/getSubscribeQrCode', params)
}
//用户登录
export function selectRole(params) {
  return http.get('deliver/web/experience/historyChatInfoDetail', params)
}
//获取url
export function getMinUrl(params) {
  return http.get('deliver/app/wechat/urlScheme?page', params)
}

//获取url
export function getMeeting(params) {
  return http.get('deliver/web/experience/meetingDetail', params)
}
//根据token获取视频id
export function getVideoId(params) {
  return http.get('znyy/app/video/ifTokenExist', params)
}