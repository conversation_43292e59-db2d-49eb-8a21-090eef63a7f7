import Vue from 'vue'
import App from './App'
//引入vuex
import {
  store
} from './store/index'
import filter from '@/common/filters.js';
import jWeixin from 'jweixin-module'
import util from '@/common/utils'
Vue.prototype.$util = util
// 全局组件

// 引入全局uView
import uView from '@/uni_modules/uview-ui'
Vue.use(uView)
// 导入全局组件注册
// import TabBar from './components/TabBar/TabBar.vue'
// Vue.component('TabBar', TabBar)
Vue.prototype.$store = store
Vue.config.productionTip = false
// if (/(Android)/i.test(navigator.userAgent)) {
//   Vue.prototype.sdk = jWeixin
// }
// if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
//   Vue.prototype.sdk = wx
// }
App.mpType = 'app'

import katex from 'katex/dist/katex.min.js';
import 'katex/dist/katex.min.css';
Vue.prototype.$katex=katex

const app = new Vue({
  store,
  ...App
})

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
import httpInterceptor from '@/common/uview.interface.js'
Vue.use(httpInterceptor, app)

app.$mount()