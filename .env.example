# 环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境 (development | production | test)
NODE_ENV=development

# UniApp平台 (h5 | mp-weixin | app-plus)
UNI_PLATFORM=h5

# API基础URL
VUE_APP_API_BASE_URL=http://localhost:3000

# 企业微信配置
VUE_APP_QY_CORPID=your_corp_id
VUE_APP_QY_AGENTID=your_agent_id
VUE_APP_QY_APPID=your_app_id

# 微信JS-SDK配置
VUE_APP_WX_APPID=your_wx_appid

# 开发服务器配置
VUE_APP_DEV_SERVER_HOST=0.0.0.0
VUE_APP_DEV_SERVER_PORT=8080

# 是否开启调试模式
VUE_APP_DEBUG=true

# 是否开启请求日志
VUE_APP_REQUEST_LOG=true

# 构建相关配置
VUE_APP_PUBLIC_PATH=./
VUE_APP_OUTPUT_DIR=unpackage/dist/build/web

# CDN配置（可选）
VUE_APP_CDN_URL=

# 其他配置
VUE_APP_TITLE=鼎校智能教育
VUE_APP_VERSION=1.0.0
