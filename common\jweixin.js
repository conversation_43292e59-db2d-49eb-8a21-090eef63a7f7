! function(e, n) {
  "function" != typeof define || !define.amd && !define.cmd || window.requirejs ? n(e, !0) : define(
    "js/jsapi/jweixin-1.0.0", [],
    function() {
      return n(e)
    })
}(window, (function(e, n) {
  if (!e.jWeixin) {
    var t, i = {
        config: "preVerifyJSAPI",
        onMenuShareTimeline: "menu:share:timeline",
        onMenuShareAppMessage: "menu:share:appmessage",
        onMenuShareWechat: "menu:share:wechat",
        onMenuShareQQ: "menu:share:qq",
        onMenuShareWeibo: "menu:share:weiboApp",
        onMenuShareQZone: "menu:share:QZone",
        previewImage: "imagePreview",
        previewFile: "previewFile",
        getLocation: "geoLocation",
        openProductSpecificView: "openProductViewWithPid",
        addCard: "batchAddCard",
        openCard: "batchViewCard",
        chooseWXPay: "getBrandWCPayRequest",
        openEnterpriseRedPacket: "getRecevieBizHongBaoRequest",
        startSearchBeacons: "startMonitoringBeacons",
        stopSearchBeacons: "stopMonitoringBeacons",
        onSearchBeacons: "onBeaconsInRange",
        consumeAndShareCard: "consumedShareCard",
        openAddress: "editAddress",
        getUserOpenID: "getUserOpenID",
        getBrandWCPayRequest: "getBrandWCPayRequest",
        notifyNativeEvent: "notifyNativeEvent"
      },
      o = function() {
        var e = {};
        for (var n in i) e[i[n]] = n;
        return e
      }(),
      a = e.document,
      r = a.title,
      c = navigator.userAgent.toLowerCase(),
      s = navigator.platform.toLowerCase(),
      d = !(!s.match("mac") && !s.match("win")),
      u = -1 != c.indexOf("wxdebugger"),
      f = -1 != c.indexOf("wxwork"),
      p = -1 != c.indexOf("android"),
      l = -1 != c.indexOf("iphone") || -1 != c.indexOf("ipad"),
      g = (t = c.match(/wxwork\/(\d+\.\d+\.\d+)/) || c.match(/wxwork\/(\d+\.\d+)/)) ? t[1] : "",
      m = {
        initStartTime: x(),
        initEndTime: 0,
        preVerifyStartTime: 0,
        preVerifyEndTime: 0
      },
      v = {
        version: "1.0.0",
        appId: "",
        initTime: 0,
        preVerifyTime: 0,
        networkType: "",
        isPreVerifyOk: 1,
        systemType: l ? 1 : p ? 2 : -1,
        clientVersion: g,
        url: encodeURIComponent(location.href)
      },
      h = {},
      S = {},
      y = {
        _completes: []
      },
      I = {
        state: 0,
        data: {}
      };
    M((function() {
      m.initEndTime = x()
    }));
    var w = {
      config: function(e) {
        h = e, L("config", e);
        var n = !1 !== h.check;
        M((function() {
          if (n) C(i.config, {
            verifyJsApiList: W(h.jsApiList)
          }, function() {
            y._complete = function(e) {
              m.preVerifyEndTime = x(), I.state = 1, I.data = e
            }, y.success = function(e) {
              v.isPreVerifyOk = 0
            }, y.fail = function(e) {
              y._fail ? y._fail(e) : I.state = -1
            };
            var e = y._completes;
            return e.push((function() {
              ! function(e) {
                if (!u && !h.debug) {
                  var n = new Image;
                  if (v.appId = h.appId, v.initTime = m.initEndTime - m.initStartTime, v
                    .preVerifyTime = m.preVerifyEndTime - m.preVerifyStartTime, d) {
                    var t = "https://open.work.weixin.qq.com/wwopen/report/sdk?v=" + v.version +
                      "&o=" + v.isPreVerifyOk + "&cv=" + v.clientVersion + "&appid=" + v.appId +
                      "&nt=pc&it=" + v.initTime + "&pt=" + v.preVerifyTime + "&url=" + v.url;
                    n.src = t
                  } else w.getNetworkType({
                    isInnerInvoke: !0,
                    success: function(e) {
                      v.networkType = e.networkType;
                      var t = "https://open.work.weixin.qq.com/wwopen/report/sdk?v=" + v
                        .version + "&o=" + v.isPreVerifyOk + "&cv=" + v.clientVersion +
                        "&appid=" + v.appId + "&nt=" + v.networkType + "&it=" + v
                        .initTime + "&pt=" + v.preVerifyTime + "&url=" + v.url;
                      n.src = t
                    }
                  })
                }
              }()
            })), y.complete = function(n) {
              for (var t = 0, i = e.length; t < i; ++t) e[t]();
              y._completes = []
            }, y
          }()), m.preVerifyStartTime = x();
          else {
            I.state = 1;
            for (var e = y._completes, t = 0, o = e.length; t < o; ++t) e[t]();
            y._completes = []
          }
        })), h.beta && D()
      },
      agentConfig: function(n) {
        M((function() {
          if (n = n || {}, e.WeixinJSBridge) {
            D();
            var t = {
              corpid: n.corpid + "",
              agentid: n.agentid + "",
              timestamp: n.timestamp + "",
              nonceStr: n.nonceStr + "",
              signature: n.signature + "",
              jsApiList: W(n.jsApiList)
            };
            WeixinJSBridge.invoke("agentConfig", _(t), (function(i) {
              S = t, e.WWOpenData ? T("agentConfig", i, n) : e.WeixinSandBox || s.match("win") ?
                WeixinJSBridge.invoke("wwapp.initWwOpenData", _({}), (function() {
                  T("agentConfig", i, n)
                })) : T("agentConfig", i, n)
            }))
          } else L("agentConfig", n)
        }))
      },
      ready: function(e) {
        0 != I.state ? e() : (y._completes.push(e), !f && h.debug && e())
      },
      error: function(e) {
        -1 == I.state ? e(I.data) : y._fail = e
      },
      checkJsApi: function(e) {
        C("checkJsApi", {
          jsApiList: W(e.jsApiList)
        }, (e._complete = function(e) {
          if (p) {
            var n = e.checkResult;
            n && (e.checkResult = JSON.parse(n))
          }
          e = function(e) {
            var n = e.checkResult;
            for (var t in n) {
              var i = o[t];
              i && (n[i] = n[t], delete n[t])
            }
            return e
          }(e)
        }, e))
      },
      onMenuShareTimeline: function(e) {
        B(i.onMenuShareTimeline, {
          complete: function() {
            C("shareTimeline", {
              title: e.title || r,
              desc: e.title || r,
              img_url: e.imgUrl || "",
              link: e.link || location.href,
              type: e.type || "link",
              data_url: e.dataUrl || "",
              enableIdTrans: e.enableIdTrans || 0
            }, e)
          }
        }, e)
      },
      onMenuShareAppMessage: function(e) {
        B(i.onMenuShareAppMessage, {
          complete: function() {
            C("sendAppMessage", {
              title: e.title || r,
              desc: e.desc || "",
              link: e.link || location.href,
              img_url: e.imgUrl || "",
              type: e.type || "link",
              data_url: e.dataUrl || "",
              finder_feed: e.finder_feed,
              finder_topic: e.finder_topic,
              finder_profile: e.finder_profile,
              enableIdTrans: e.enableIdTrans || 0
            }, e)
          }
        }, e)
      },
      onMenuShareWechat: function(e) {
        B(i.onMenuShareWechat, {
          complete: function() {
            C("shareWechat", {
              title: e.title || r,
              desc: e.desc || "",
              link: e.link || location.href,
              img_url: e.imgUrl || "",
              type: e.type || "link",
              data_url: e.dataUrl || "",
              enableIdTrans: e.enableIdTrans || 0
            }, e)
          }
        }, e)
      },
      onMenuShareQQ: function(e) {
        B(i.onMenuShareQQ, {
          complete: function() {
            C("shareQQ", {
              title: e.title || r,
              desc: e.desc || "",
              img_url: e.imgUrl || "",
              link: e.link || location.href
            }, e)
          }
        }, e)
      },
      onMenuShareWeibo: function(e) {
        B(i.onMenuShareWeibo, {
          complete: function() {
            C("shareWeiboApp", {
              title: e.title || r,
              desc: e.desc || "",
              img_url: e.imgUrl || "",
              link: e.link || location.href
            }, e)
          }
        }, e)
      },
      onMenuShareQZone: function(e) {
        B(i.onMenuShareQZone, {
          complete: function() {
            C("shareQZone", {
              title: e.title || r,
              desc: e.desc || "",
              img_url: e.imgUrl || "",
              link: e.link || location.href
            }, e)
          }
        }, e)
      },
      startRecord: function(e) {
        C("startRecord", {}, e)
      },
      stopRecord: function(e) {
        C("stopRecord", {}, e)
      },
      onVoiceRecordEnd: function(e) {
        B("onVoiceRecordEnd", e)
      },
      playVoice: function(e) {
        C("playVoice", {
          localId: e.localId
        }, e)
      },
      pauseVoice: function(e) {
        C("pauseVoice", {
          localId: e.localId
        }, e)
      },
      stopVoice: function(e) {
        C("stopVoice", {
          localId: e.localId
        }, e)
      },
      onVoicePlayEnd: function(e) {
        B("onVoicePlayEnd", e)
      },
      uploadVoice: function(e) {
        C("uploadVoice", {
          localId: e.localId,
          isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1
        }, e)
      },
      downloadVoice: function(e) {
        C("downloadVoice", {
          serverId: e.serverId,
          isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1
        }, e)
      },
      translateVoice: function(e) {
        C("translateVoice", {
          localId: e.localId,
          isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1
        }, e)
      },
      chooseImage: function(e) {
        C("chooseImage", {
          scene: "1|2",
          count: e.count || 9,
          sizeType: e.sizeType || ["original", "compressed"],
          sourceType: e.sourceType || ["album", "camera"],
          defaultCameraMode: e.defaultCameraMode || "normal",
          isSaveToAlbum: 0 == e.isSaveToAlbum ? 0 : 1
        }, (e._complete = function(e) {
          if (p) {
            var n = e.localIds;
            n && (e.localIds = JSON.parse(n))
          }
        }, e))
      },
      previewImage: function(e) {
        C(i.previewImage, {
          current: e.current,
          urls: e.urls
        }, e)
      },
      uploadImage: function(e) {
        C("uploadImage", {
          localId: e.localId,
          isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1
        }, e)
      },
      downloadImage: function(e) {
        C("downloadImage", {
          serverId: e.serverId,
          isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1
        }, e)
      },
      getLocalImgData: function(e) {
        C("getLocalImgData", {
          localId: e.localId,
          success: e.success
        }, e)
      },
      previewFile: function(e) {
        C("previewFile", {
          url: e.url,
          name: e.name,
          size: e.size
        }, e)
      },
      getNetworkType: function(e) {
        C("getNetworkType", {}, (e._complete = function(e) {
          e = function(e) {
            var n = e.errMsg;
            e.errMsg = "getNetworkType:ok";
            var t = e.subtype;
            if (delete e.subtype, t) e.networkType = t;
            else {
              var i = n.indexOf(":"),
                o = n.substring(i + 1);
              switch (o) {
                case "wifi":
                case "edge":
                case "wwan":
                  e.networkType = o;
                  break;
                default:
                  e.errMsg = "getNetworkType:fail"
              }
            }
            return e
          }(e)
        }, e))
      },
      openLocation: function(e) {
        C("openLocation", {
          latitude: e.latitude,
          longitude: e.longitude,
          name: e.name || "",
          address: e.address || "",
          scale: e.scale || 28,
          infoUrl: e.infoUrl || ""
        }, e)
      },
      getLocation: function(e) {
        e = e || {}, C(i.getLocation, {
          type: e.type || "wgs84"
        }, (e._complete = function(e) {
          delete e.type
        }, e))
      },
      hideOptionMenu: function(e) {
        C("hideOptionMenu", {}, e)
      },
      showOptionMenu: function(e) {
        C("showOptionMenu", {}, e)
      },
      closeWindow: function(e) {
        C("closeWindow", {}, e = e || {})
      },
      hideMenuItems: function(e) {
        C("hideMenuItems", {
          menuList: e.menuList
        }, e)
      },
      showMenuItems: function(e) {
        C("showMenuItems", {
          menuList: e.menuList
        }, e)
      },
      hideAllNonBaseMenuItem: function(e) {
        C("hideAllNonBaseMenuItem", {}, e)
      },
      showAllNonBaseMenuItem: function(e) {
        C("showAllNonBaseMenuItem", {}, e)
      },
      scanQRCode: function(e) {
        C("scanQRCode", {
          needResult: (e = e || {}).needResult || 0,
          scanType: e.scanType || ["qrCode", "barCode"]
        }, (e._complete = function(e) {
          if (l) {
            var n = e.resultStr;
            if (n) {
              var t = JSON.parse(n);
              e.resultStr = t && t.scan_code && t.scan_code.scan_result
            }
          }
        }, e))
      },
      openAddress: function(e) {
        C(i.openAddress, {}, (e._complete = function(e) {
          e = function(e) {
            return e.postalCode = e.addressPostalCode, delete e.addressPostalCode, e.provinceName = e
              .proviceFirstStageName, delete e.proviceFirstStageName, e.cityName = e
              .addressCitySecondStageName, delete e.addressCitySecondStageName, e.countryName = e
              .addressCountiesThirdStageName, delete e.addressCountiesThirdStageName, e.detailInfo = e
              .addressDetailInfo, delete e.addressDetailInfo, e
          }(e)
        }, e))
      },
      openProductSpecificView: function(e) {
        C(i.openProductSpecificView, {
          pid: e.productId,
          view_type: e.viewType || 0,
          ext_info: e.extInfo
        }, e)
      },
      addCard: function(e) {
        for (var n = e.cardList, t = [], o = 0, a = n.length; o < a; ++o) {
          var r = n[o],
            c = {
              card_id: r.cardId,
              card_ext: r.cardExt
            };
          t.push(c)
        }
        C(i.addCard, {
          card_list: t
        }, (e._complete = function(e) {
          var n = e.card_list;
          if (n) {
            for (var t = 0, i = (n = JSON.parse(n)).length; t < i; ++t) {
              var o = n[t];
              o.cardId = o.card_id, o.cardExt = o.card_ext, o.isSuccess = !!o.is_succ, delete o.card_id,
                delete o.card_ext, delete o.is_succ
            }
            e.cardList = n, delete e.card_list
          }
        }, e))
      },
      chooseCard: function(e) {
        C("chooseCard", {
          app_id: h.appId,
          location_id: e.shopId || "",
          sign_type: e.signType || "SHA1",
          card_id: e.cardId || "",
          card_type: e.cardType || "",
          card_sign: e.cardSign,
          time_stamp: e.timestamp + "",
          nonce_str: e.nonceStr
        }, (e._complete = function(e) {
          e.cardList = e.choose_card_info, delete e.choose_card_info
        }, e))
      },
      openCard: function(e) {
        for (var n = e.cardList, t = [], o = 0, a = n.length; o < a; ++o) {
          var r = n[o],
            c = {
              card_id: r.cardId,
              code: r.code
            };
          t.push(c)
        }
        C(i.openCard, {
          card_list: t
        }, e)
      },
      consumeAndShareCard: function(e) {
        C(i.consumeAndShareCard, {
          consumedCardId: e.cardId,
          consumedCode: e.code
        }, e)
      },
      chooseWXPay: function(e) {
        C(i.chooseWXPay, k(e), e)
      },
      openEnterpriseRedPacket: function(e) {
        C(i.openEnterpriseRedPacket, k(e), e)
      },
      startSearchBeacons: function(e) {
        C(i.startSearchBeacons, {
          ticket: e.ticket
        }, e)
      },
      stopSearchBeacons: function(e) {
        C(i.stopSearchBeacons, {}, e)
      },
      onSearchBeacons: function(e) {
        B(i.onSearchBeacons, e)
      },
      openEnterpriseChat: function(e) {
        C("openEnterpriseChat", {
          useridlist: e.userIds,
          chatname: e.groupName,
          userIds: e.userIds,
          groupName: e.groupName,
          openIds: e.openIds,
          chatId: e.chatId,
          externalUserIds: e.externalUserIds
        }, e)
      },
      getUserOpenID: function(e) {
        C("fetchWXOpenIdRequest", {}, e)
      },
      getBrandWCPayRequest: function(e) {
        C("getBrandWCPayRequest", {
          appId: e.appId,
          partnerId: e.partnerId,
          openId: e.openId,
          timeStamp: e.timeStamp,
          nonceStr: e.nonceStr,
          package: e.package,
          signType: e.signType,
          paySign: e.paySign
        }, e)
      },
      onHistoryBack: function(e) {
        B("onHistoryBack", {
          complete: function() {
            if ("function" == typeof e) try {
              var n = e();
              if (!1 === n || 0 === n) return !1
            } catch (e) {}
            return C("historyBack"), !1
          }
        })
      },
      notifyNativeEvent: function(e) {
        C("notifyNativeEvent", {
          event: e.event,
          resultCode: e.resultCode,
          data: e.data
        }, e)
      },
      openBluetoothAdapter: function(e) {
        C("openBluetoothAdapter", {}, e)
      },
      closeBluetoothAdapter: function(e) {
        C("closeBluetoothAdapter", {}, e)
      },
      getBluetoothAdapterState: function(e) {
        C("getBluetoothAdapterState", {}, e)
      },
      onBluetoothAdapterStateChange: function(e) {
        B("onBluetoothAdapterStateChange", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      startBluetoothDevicesDiscovery: function(e) {
        C("startBluetoothDevicesDiscovery", {
          services: e.services || [],
          allowDuplicatesKey: e.allowDuplicatesKey || !1,
          interval: e.interval || 0
        }, e)
      },
      stopBluetoothDevicesDiscovery: function(e) {
        C("stopBluetoothDevicesDiscovery", {}, e)
      },
      getBluetoothDevices: function(e) {
        C("getBluetoothDevices", {}, {
          success: function(n) {
            if (n && n.devices)
              for (var t = 0; t < n.devices.length; t++) n.devices[t].advertisData = E(n.devices[t]
                .advertisData), n.devices[t].serviceData = E(n.devices[t].serviceData);
            "function" == typeof e.success && e.success(n)
          },
          fail: e.fail,
          complete: e.complete
        })
      },
      onBluetoothDeviceFound: function(e) {
        B("onBluetoothDeviceFound", {
          complete: function(n) {
            if (n && n.devices)
              for (var t = 0; t < n.devices.length; t++) n.devices[t].advertisData = E(n.devices[t]
                .advertisData);
            "function" == typeof e && e(n ? n.devices : n)
          }
        })
      },
      getConnectedBluetoothDevices: function(e) {
        C("getConnectedBluetoothDevices", {
          services: e.services
        }, e)
      },
      createBLEConnection: function(e) {
        C("createBLEConnection", {
          deviceId: e.deviceId
        }, e)
      },
      closeBLEConnection: function(e) {
        C("closeBLEConnection", {
          deviceId: e.deviceId
        }, e)
      },
      onBLEConnectionStateChange: function(e) {
        B("onBLEConnectionStateChange", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      getBLEDeviceServices: function(e) {
        C("getBLEDeviceServices", {
          deviceId: e.deviceId
        }, e)
      },
      readBLECharacteristicValue: function(e) {
        C("readBLECharacteristicValue", {
          deviceId: e.deviceId,
          serviceId: e.serviceId,
          characteristicId: e.characteristicId
        }, e)
      },
      getBLEDeviceCharacteristics: function(e) {
        C("getBLEDeviceCharacteristics", {
          deviceId: e.deviceId,
          serviceId: e.serviceId
        }, e)
      },
      writeBLECharacteristicValue: function(n) {
        "object" == typeof n && n.value && (n.value = function(n) {
          if (void 0 === n) return n;
          for (var t = "", i = new Uint8Array(n), o = i.byteLength, a = 0; a < o; a++) t += String
            .fromCharCode(i[a]);
          return e.btoa(t)
        }(n.value)), C("writeBLECharacteristicValue", {
          deviceId: n.deviceId,
          serviceId: n.serviceId,
          characteristicId: n.characteristicId,
          value: n.value
        }, n)
      },
      notifyBLECharacteristicValueChange: function(e) {
        C("notifyBLECharacteristicValueChange", {
          deviceId: e.deviceId,
          serviceId: e.serviceId,
          characteristicId: e.characteristicId,
          state: e.state
        }, e)
      },
      onBLECharacteristicValueChange: function(e) {
        B("onBLECharacteristicValueChange", {
          complete: function(n) {
            n && n.value && (n.value = E(n.value)), "function" == typeof e && e(n)
          }
        })
      },
      startBeaconDiscovery: function(e) {
        C("startBeaconDiscovery", {
          uuids: e.uuids
        }, e)
      },
      stopBeaconDiscovery: function(e) {
        C("stopBeaconDiscovery", {}, e)
      },
      getBeacons: function(e) {
        C("getBeacons", {}, e)
      },
      onBeaconUpdate: function(e) {
        B("onBeaconUpdate", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      onBeaconServiceChange: function(e) {
        B("onBeaconServiceChange", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      startWifi: function(e) {
        C("startWifi", {}, e)
      },
      stopWifi: function(e) {
        C("stopWifi", {}, e)
      },
      connectWifi: function(e) {
        var n = {
          SSID: e.SSID,
          BSSID: e.BSSID
        };
        e.hasOwnProperty("password") && (n.password = e.password), C("connectWifi", n, e)
      },
      getWifiList: function(e) {
        C("getWifiList", {}, e)
      },
      onGetWifiList: function(e) {
        B("onGetWifiList", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      onWifiConnected: function(e) {
        B("onWifiConnected", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      getConnectedWifi: function(e) {
        C("getConnectedWifi", {}, e)
      },
      setClipboardData: function(e) {
        C("setClipboardData", {
          data: e.data
        }, e)
      },
      getClipboardData: function(e) {
        C("getClipboardData", {}, e)
      },
      onNetworkStatusChange: function(e) {
        B("onNetworkStatusChange", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      onLocationChange: function(e) {
        B("auto:location:report", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      onUserCaptureScreen: function(e) {
        B("onUserCaptureScreen", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      },
      onKeyboardChange: function(e) {
        B("onKeyboardChange", {
          complete: function(n) {
            "function" == typeof e && e(n)
          }
        })
      }
    };
    return n && (e.wx = e.jWeixin = w), w
  }

  function C(n, t, i) {
    e.WeixinJSBridge ? WeixinJSBridge.invoke(n, _(t), (function(e) {
      T(n, e, i)
    })) : L(n, i)
  }

  function B(n, t, i) {
    e.WeixinJSBridge ? WeixinJSBridge.on(n, (function(e) {
      i && i.trigger && i.trigger(e), T(n, e, t)
    })) : L(n, i || t)
  }

  function _(e) {
    e = e || {};
    var n = {};
    return h.appId ? n = h : S.corpid && ((n = S).appId = S.corpid), e.appId = n.appId, e.verifyAppId = n.appId, e
      .verifySignType = "sha1", e.verifyTimestamp = n.timestamp + "", e.verifyNonceStr = n.nonceStr, e
      .verifySignature = n.signature, e
  }

  function k(e) {
    return {
      timeStamp: e.timestamp + "",
      nonceStr: e.nonceStr,
      package: e.package,
      paySign: e.paySign,
      signType: e.signType || "SHA1"
    }
  }

  function T(e, n, t) {
    "openEnterpriseChat" == e && (n.errCode = n.err_code), delete n.err_code, delete n.err_desc, delete n
      .err_detail;
    var i = n.errMsg;
    i || (i = n.err_msg, delete n.err_msg, i = function(e, n) {
        var t = e,
          i = o[t];
        i && (t = i);
        var a = "ok";
        if (n) {
          var r = n.indexOf(":");
          "confirm" == (a = n.substring(r + 1)) && (a = "ok"), "failed" == a && (a = "fail"), -1 != a.indexOf(
              "failed_") && (a = a.substring(7)), -1 != a.indexOf("fail_") && (a = a.substring(5)),
            "access denied" != (a = (a = a.replace(/_/g, " ")).toLowerCase()) && "no permission to execute" !=
            a || (a = "permission denied"), "config" == t && "function not exist" == a && (a = "ok"), "" == a && (
              a = "fail")
        }
        return n = t + ":" + a
      }(e, i), n.errMsg = i), (t = t || {})._complete && (t._complete(n), delete t._complete), i = n.errMsg || "", h
      .debug && !t.isInnerInvoke && alert(JSON.stringify(n));
    var a = i.indexOf(":");
    switch (i.substring(a + 1)) {
      case "ok":
        t.success && t.success(n);
        break;
      case "cancel":
        t.cancel && t.cancel(n);
        break;
      default:
        t.fail && t.fail(n)
    }
    t.complete && t.complete(n)
  }

  function W(e) {
    if (e) {
      for (var n = 0, t = e.length; n < t; ++n) {
        var o = e[n],
          a = i[o];
        a && (e[n] = a)
      }
      return e
    }
  }

  function L(e, n) {
    if (!(!h.debug || n && n.isInnerInvoke)) {
      var t = o[e];
      t && (e = t), n && n._complete && delete n._complete, console.log('"' + e + '",', n || "")
    }
  }

  function x() {
    return (new Date).getTime()
  }

  function M(n) {
    f && (e.WeixinJSBridge ? n() : a.addEventListener && a.addEventListener("WeixinJSBridgeReady", n, !1))
  }

  function D() {
    w.invoke || (w.invoke = function(n, t, i) {
      e.WeixinJSBridge && WeixinJSBridge.invoke(n, _(t), i)
    }, w.on = function(n, t) {
      e.WeixinJSBridge && WeixinJSBridge.on(n, t)
    })
  }

  function E(n) {
    if (void 0 === n) return n;
    for (var t = e.atob(n), i = t.length, o = new Uint8Array(i), a = 0; a < i; a++) o[a] = t.charCodeAt(a);
    return o.buffer
  }
})),
function(e) {
  var n = e.encodeURIComponent;
  try {
    e.wwperf = {
      config: function(n) {
        var i = e.performance,
          o = e.document,
          a = i.timing,
          r = !1;
        try {
          if (!e.navigator.userAgent.toLowerCase().match("wxwork")) return;
          e.WeixinJSBridge ? c() : o.addEventListener("WeixinJSBridgeReady", c, !1), "complete" === o.readyState ?
            c() : e.addEventListener("load", c, !1)
        } catch (e) {
          return t(e, n)
        }

        function c() {
          try {
            e.WeixinJSBridge && "complete" === o.readyState && e.setTimeout(s, 0)
          } catch (e) {
            return t(e, n)
          }
        }

        function s() {
          try {
            if (r) return;
            r = !0;
            var o = -1;
            i.navigation && null != i.navigation.redirectCount && (o = i.navigation.redirectCount);
            var c = -1;
            if ("function" == typeof i.getEntries) {
              var s = i.getEntries();
              c = 0;
              for (var d = s.length - 1; d >= 0; d--) "resource" == s[d].entryType && (c += 1)
            }
            e.WeixinJSBridge.invoke("innerSaveWebPerformance", {
              perf_data: JSON.stringify({
                env: n,
                url: e.location.href,
                redirect_count: o,
                resource_count: c,
                dns_time: Math.max(0, a.domainLookupEnd - a.domainLookupStart),
                connect_time: Math.max(0, a.connectEnd - a.connectStart),
                request_time: Math.max(0, a.responseEnd - a.requestStart),
                loading_time: Math.max(0, a.domLoading - a.navigationStart),
                interactive_time: Math.max(0, a.domInteractive - a.navigationStart),
                complete_time: Math.max(0, a.domComplete - a.navigationStart),
                log_time: Math.floor(+new Date / 1e3)
              })
            }, (function() {}))
          } catch (e) {
            return t(e, n)
          }
        }
      }
    }
  } catch (e) {
    return t(e)
  }

  function t(t, i) {
    try {
      var o = new e.Image,
        a = JSON.stringify({
          msg: t.message,
          stack: t.stack,
          env: i
        });
      o.src = "//badjs2.qq.com/badjs?id=1385&uin=1008&from=" + n(e.location.href) + "&msg[0]=" + n(a) +
        "&target[0]=wwperf.js&level[0]=4&count=1&_t=" + +new Date
    } catch (t) {}
  }
}(this);