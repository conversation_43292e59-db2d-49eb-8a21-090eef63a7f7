<template>
  <view>
    <view class="xkt_table">
      <!-- 模块 1：课堂实时监测 -->
      <view class="table_title">课堂实时监测</view>

      <!-- 学习准备 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose" style="width: 128rpx"><view>学习准备</view></view>

        <!-- 1.预习任务完成 -->
        <view class="radio_title">1.预习任务完成</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="addFeedbackTableCo.preparationCompleted == itemLa.value ? 'active' : ''"
            @click="addFeedbackTableClick(itemLa, 'preparationCompleted')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.preparationCompleted == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>

        <!-- 2.进门测试题目正确率 -->
        <view class="radio_title">2.进门测试题目正确率</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.entryTestAccuracy == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'entryTestAccuracy')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.entryTestAccuracy == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
        <view class="mark" style="margin-top: 29rpx">
          <u--textarea
            v-model="addFeedbackTableCo.entryTestRate"
            :value="addFeedbackTableCo.entryTestRate"
            placeholder="备注（详细记录），请输入……"
            count
            placeholderStyle="color:#b0b0b6"
            height="146rpx"
            maxlength="30"
            border="none"
            class="custom-textarea"
          ></u--textarea>
        </view>
      </view>

      <!-- 注意力集中度 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose"><view>注意力集中度</view></view>

        <view class="radio_title">1.眼神跟随教师/屏幕</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.eyeFollowingScore == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'eyeFollowingScore')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.eyeFollowingScore == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>

        <view class="radio_title">2.无关操作<=2次/节课</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.irrelevantActions == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'irrelevantActions')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.irrelevantActions == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
      </view>

      <!-- 课堂参与度 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose"><view>课堂参与度</view></view>

        <view class="radio_title">1.主动回答问题>=1次/节课</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="addFeedbackTableCo.activeAnswers == itemLa.value ? 'active' : ''"
            @click="addFeedbackTableClick(itemLa, 'activeAnswers')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.activeAnswers == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>

        <view class="radio_title">2.互动次数和质量</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.interactionCount == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'interactionCount')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.interactionCount == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
      </view>

      <!-- 模块 2：教学效果观察 -->
      <view class="table_title">教学效果观察</view>

      <!-- 知识掌握 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose" style="width: 128rpx"><view>知识掌握</view></view>

        <view class="radio_title">1.课后留题正确率>=70%</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.postClassAccuracy == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'postClassAccuracy')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.postClassAccuracy == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>

        <view class="radio_title">2.课后错题讲解+知识点回顾质量</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'mistakeKnowledgeReviewQuality')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
      </view>

      <!-- 精彩时刻 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose" style="width: 128rpx"><view>精彩时刻</view></view>

        <view class="radio_title">1.听课状态</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.learningStatus == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'learningStatus')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningStatus == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
      </view>

      <!-- 情绪与态度 -->
      <view style="padding: 27rpx 30rpx; font-weight: 400">
        <view class="study_choose" style="width: 152rpx"><view>情绪与态度</view></view>

        <view class="radio_title">1.学习积极性</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.learningEngagement == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'learningEngagement')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningEngagement == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>

        <view class="radio_title">2.是否有厌学情绪，抵触心理</view>
        <view class="radio-group">
          <view
            v-for="itemLa in tableOptions"
            :key="itemLa.value"
            class="radio-item"
            :class="{ active: addFeedbackTableCo.resistanceSigns == itemLa.value }"
            @click="addFeedbackTableClick(itemLa, 'resistanceSigns')"
          >
            <view class="circle"><view class="dot" v-if="addFeedbackTableCo.resistanceSigns == itemLa.value"></view></view>
            <text class="label">{{ itemLa.label }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tableOptions: [
        { label: '✔', value: '1' },
        { label: '✘', value: '2' },
        { label: '无', value: '3' }
      ],
      addFeedbackTableCo: {
        preparationCompleted: null, //预习任务完成
        entryTestAccuracy: null, //进门测试题目正确率
        entryTestRate: '', //进门测试正确率(%)
        eyeFollowingScore: null, //眼神跟随教师/屏幕
        irrelevantActions: null, //无关操作次数<=2次/节课
        activeAnswers: null, //主动回答问题>=1次/节课
        interactionCount: null, //互动总次数和质量
        postClassAccuracy: null, //课后留题正确率>=70%
        mistakeKnowledgeReviewQuality: null, //错题讲解质量+知识点回顾
        learningStatus: null, //听课状态
        learningEngagement: null, //学习积极性评分
        resistanceSigns: null //厌学/抵触情绪迹象
      },
      customStyle: {
        width: '631rpx',
        height: '161rpx',
        backgroundColor: 'rgb(247, 247, 247)',
        borderRadius: '8rpx'
      }
    };
  },
  created() {
    console.log('h5h5h5h', this.$route.query, this.$route);
    this.addFeedbackTableCo = JSON.parse(this.$route.query.data);
  },
  methods: {
    addFeedbackTableClick(itemLa, prop) {
      this.$set(this.addFeedbackTableCo, prop, itemLa.value);
    }
  }
};
</script>

<style>
.xkt_table {
  box-sizing: border-box;
  /*  width: 690rpx; */
  height: 2660rpx;
  padding: 20rpx 0;
  /* margin-top: 26rpx; */
  background: url('https://document.dxznjy.com/dxSelect/751a75dd-02e3-40a8-bc8d-4d26ce8a59cd.png');
  .table_title {
    width: 195rpx;
    height: 57rpx;
    line-height: 57rpx;
    border-radius: 0 28.5rpx 28.5rpx 0;
    color: #fff;
    background-color: #1dd39a;
    padding-left: 33rpx;
    font-size: 28rpx;
    font-weight: 700;
  }
  .study_choose {
    box-sizing: border-box;
    min-width: 120rpx;
    max-width: 176rpx;
    height: 40rpx;
    padding: 0 13rpx;
    line-height: 40rpx;
    text-align: center;
    border-radius: 8rpx;
    background-color: rgba(191, 240, 213, 0.4);
    border: 2rpx solid #5fcd91;
    font-size: 24rpx;
    color: #52be92;
    font-weight: 400;
    margin-bottom: 24rpx;
  }
  .radio_title {
    height: 50rpx;
    line-height: 50rpx;
    font-size: 28rpx;
    margin-top: 24rpx;
  }
  .radio-group {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
  }

  .radio-item {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .circle {
    width: 34rpx;
    height: 34rpx;
    border: 2rpx solid #979797;
    border-radius: 50%;
    margin-right: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dot {
    width: 18rpx;
    height: 18rpx;
    background-color: #1dd39a;
    border-radius: 50%;
  }

  .radio-item.active .circle {
    border-color: #1dd39a;
  }

  .label {
    font-size: 32rpx;
    color: #333;
  }
  .mark {
    .u-textarea__field {
      // width: 631rpx;
      height: 161rpx;
      background-color: rgb(247, 247, 247);
      border-radius: 8rpx;
      border: none !important;
    }
    .u-textarea__count {
      position: absolute !important;
      right: 30rpx !important;
      bottom: 26rpx !important;
      font-size: 28rpx !important;
      background-color: transparent !important;
    }
    .uni-textarea-compute,
    .uni-textarea-line,
    .uni-textarea-placeholder,
    .uni-textarea-textarea {
      box-sizing: border-box !important;
      width: 631rpx !important;
      padding: 10rpx 20rpx !important;
    }
  }
}
</style>
