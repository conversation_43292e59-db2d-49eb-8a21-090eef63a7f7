<template>
  <view>
    <view id="player"></view>
  </view>
</template>

<script>
  import { getVideoId } from '@/api/user.js';
  export default {
    data() {
      return {
        vodPlayerJs: 'https://player.polyv.net/resp/vod-player/latest/player.js',
        vid: ''
      };
    },
    methods: {
      loadPlayerScript(callback) {
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },

      loadPlayer() {
        let that = this;
        let h = '';
        let w = '';
        uni.getSystemInfo({
          success: (res) => {
            // 可使用窗口高度，将px转换rpx
            console.log(res);
            h = res.windowHeight;
            w = res.windowWidth;
          }
        });
        const polyvPlayer = window.polyvPlayer;
        that.player = polyvPlayer({
          autoplay: true,
          wrap: '#player',
          width: w ? w : 800,
          height: h ? h : 533,
          cover_display: 'scaleAspectFit',
          // df: 3,
          vid: that.vid
        });
      }
    },
    destroyed() {
      if (this.player) {
        this.player.destroy();
      }
    },
    async onLoad(e) {
      let that = this;
      if (e.token) {
        uni.showLoading({ title: '加载中' });
        // VIDEO_1311273965390680064
        let res = await getVideoId({ token: e.token });
        if (res) {
          that.vid = res;
          that.$nextTick(() => {
            that.loadPlayerScript(that.loadPlayer);
          });
          uni.hideLoading();
        } else {
          uni.showToast({
            title: '未找到对应视频',
            icon: 'none'
          });
        }
      }
    },
    mounted() {
      // this.loadPlayerScript(this.loadPlayer);
    }
  };
</script>

<style lang="scss"></style>
