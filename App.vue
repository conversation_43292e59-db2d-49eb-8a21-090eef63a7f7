<script>
export default {
  onLaunch: function () {
    console.log('App Launch');
    // var script = document.createElement('script');
    // script.src = 'https://res.wx.qq.com/open/js/jweixin-1.2.0.js';
    // document.head.appendChild(script);

    // 动态引入script/uni的SDK
    var script = document.createElement('script');
    script.src = './static/utils/uni.webview.1.5.5.js..es';
    script.type = 'text/javascript';
    document.body.appendChild(script);
  },
  onShow: function () {
    console.log('App Show');
  },
  onHide: function () {
    console.log('App Hide');
  },
}
</script>

<style lang="scss">
@import 'katex/dist/katex.min.css';
@import '@/uni_modules/uview-ui/index.scss';
</style>