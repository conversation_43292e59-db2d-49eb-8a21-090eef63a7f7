import { Plugin, Events } from 'xgplayer'

const { POSITIONS } = Plugin

// demoPlugin.js
export default class demoPlugin extends Plugin {

  timer = null;
  currentTime = '00:00:00';
  // 插件的名称，将作为插件实例的唯一key值
  static get pluginName() {
    return 'storyNodes'
  }

  static get defaultConfig() {
    return {
      position: POSITIONS.ROOT
    }
  }

  constructor(args) {
    super(args)
  }

  beforePlayerInit() {
    // TODO 播放器调用start初始化播放源之前的逻辑
  }

  afterPlayerInit() {
    // TODO 播放器调用start初始化播放源之后的逻辑
  }

  afterCreate() {
  }
  // 更新DOM
  update(title) {
    if (title) {
      this.setHtml(`<div class="story-nodes-item" >${title}</div>`, () => {
        // console.log('dom重置完成')
        if (title) {
          setTimeout(() => {
            this.update('')
          }, 1000)
        }
      })
    } else {
      this.setHtml(`<div >${title}</div>`, () => { })
    }

  }

  destroy() {
    this.unbind('.icon', 'click', this.onIconClick)
    this.unbind('click', this.onClick)
    this.icon = null
    // 播放器销毁的时候一些逻辑
  }

  render() {
    return `<div class="story-nodes"></div>`
  }
}