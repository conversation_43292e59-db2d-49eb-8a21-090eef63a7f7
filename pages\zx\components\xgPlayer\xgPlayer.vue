<template>
  <div class="container">
    <div id="mse"></div>
  </div>
</template>

<script>
import DynamicBg from 'xgplayer/es/plugins/dynamicBg'
import Player, { Plugin, Events } from 'xgplayer';
import topPlugin from './plugin/topPlugin'
import storyNodes from './plugin/storyNodes'
import 'xgplayer/dist/index.min.css';
import './index.css'
export default {
  props: {
    currentVideo: {
      type: Object,
      default: () => { }
    },
    isIos:{ // 是否是IOS
      type: Boolean,
      default: false
    },
    isRotate:{ // 是否是旋转屏幕
      type: Boolean,
      default: false
    }
  },
  // 监听器
  watch: {
    currentVideo: function (newVal, oldVal) {
      this.init()
    },
  },
  data() {
    return {
      // 进度条时间参考数
      progressTime: 0,
      progressDot: [
        {
          id: 0,         // 唯一标识，用于删除的时候索引
          time: 10,      // 展示的时间点，例子为在播放到10s钟的时候展示
          text: 'DemoDemo10',  // hover的时候展示文案，可以为空
          duration: 1.5,   // 展示时间跨度，单位为s
          style: {       // 指定样式
            backgroundColor: '#FFDE56'
          },
        },
        {
          id: 0,         // 唯一标识，用于删除的时候索引
          time: 30,      // 展示的时间点，例子为在播放到10s钟的时候展示
          text: 'DemoDemo30',  // hover的时候展示文案，可以为空
          duration: 1.5,   // 展示时间跨度，单位为s
          style: {       // 指定样式
            backgroundColor: '#FFDE56'
          },
        },
        {
          id: 0,         // 唯一标识，用于删除的时候索引
          time: 30,      // 展示的时间点，例子为在播放到10s钟的时候展示
          text: 'DemoDemo45',  // hover的时候展示文案，可以为空
          duration: 1.5,   // 展示时间跨度，单位为s
          style: {       // 指定样式
            backgroundColor: '#FFDE56'
          },
        },
      ],
      storyNodes: null,// 节点插件实例
      topPlugin: null, // top插件实例
      timer: null, // top插件定时器
      mobile: { timer: null }, // 移动端插件实例
      progresspreview: null, // 进度条预览插件实例
      isFullscreen: false, // 是否全屏
    }
  },
  mounted() {
    // this.init()
  },
  methods: {
    init() {
      let that = this
      this.player = new Player({
        id: 'mse',
        lang: 'zh-cn',
        url: this.currentVideo.url,
        poster: this.currentVideo.cover,
        width: '100%',
        height: '100%',
        commonStyle: {
          // 播放完成部分进度条底色
          playedColor: '#42B983',
        },
        startTime: this.currentVideo.startTime,  // 开始时间，单位为s
        seekedStatus: 'auto',  // 拖动进度条后是否自动播放，可选值：play 播放, pause 暂停, auto 保持操作前的状态
        inactive: 3000, // 鼠标静止超过多少ms后隐藏控制栏
        topBarAutoHide: true,  // topBar是否自动隐藏
        defaultPlaybackRate: 1,  // 默认播放速率
        fluid: true, // 自适应宽高
        progressDot: [],  // 进度点配置
        plugins: [topPlugin, storyNodes, DynamicBg],
        playbackRate: { // 播放速率切换
          hidePortrait: true, // 是否在竖屏状态下隐藏
        },
        definition: { // 清晰度切换
          hidePortrait: true, // 是否在竖屏状态下隐藏
          defaultDefinition: this.currentVideo.transcodeInfos.length && this.currentVideo.transcodeInfos.length > 0 && this.currentVideo.transcodeInfos[this.currentVideo.transcodeInfos.length - 1].definition, // 默认清晰度
        },
        miniprogress: true,
        storyNodes: {
          index: 2
        },
        // 'x5-video-player-fullscreen': false, // 禁用x5全屏插件
        // "x5-video-player-type": "h5",
        // 动态背景
        // dynamicBg: {
        //   disable: false
        // },
        mobile: {
          // disableGesture: true, // 禁用手势控制
          isTouchingSeek: true //是否在touchMove的同时更新播放器的currentTime
        },
        fullscreen: {
          useCssFullscreen: this.isIos ? true : false, // 是否使用css全屏
          // needBackIcon: true, // 全屏按钮是否显示返回图标
          rotateFullscreen: this.isIos && this.isRotate ? true : false, // 旋转屏幕时是否全屏
        },
        // rotate:{
        //   disable:  this.isIos && this.isRotate ? false: true, // 显示旋转按钮
        //   innerRotate: true, // 内部旋转
        //   clockwise: true
        // },
        ignores: ['volume'] // 忽略的插件
      });
      // 添加清晰度
      if (this.currentVideo.transcodeInfos && this.currentVideo.transcodeInfos.length > 0) {
        this.player.emit('resourceReady', this.currentVideo.transcodeInfos);
        // console.log("🚀 ~ init ~ this.currentVideo:", this.currentVideo)
      }
      // 调用接口注册插件
      // DynamicBg
      this.player.registerPlugin(topPlugin, storyNodes)
      // 视频预加载完成
      this.player.on(Events.LOADED_DATA, () => {
        this.progresspreview = this.player.getPlugin('progresspreview')
        // updateAllDots
        // 进度条时间参考数
        this.progressTime = this.player.duration / 50
        this.currentVideo.storyNodes.forEach(item => {
          item.duration = this.progressTime
        })
        // 监听全屏事件
        this.player.on(Events.FULLSCREEN_CHANGE, (isFullscreen) => {
          if (isFullscreen) {
            // 全屏TODO
            this.isFullscreen = true
            // 更新进度条节点
            this.updateNodes()
          } else {
            // 退出全屏TODO
            this.isFullscreen = false
            this.progresspreview.updateAllDots()
          }
        })
      })
      // 进度条时间更新
      this.player.on(Events.TIME_UPDATE, () => {
        if (!this.progresspreview || !this.isFullscreen) return;
        this.updateNodes()
      })
      // 播放器准备就绪
      this.player.on(Events.READY, () => {
        // 获取节点插件实例
        this.topPlugin = this.player.getPlugin('topPlugin')
        this.storyNodes = this.player.getPlugin('storyNodes')
        // 开始
        this.player.on(Events.PLAY, () => {
          this.timer = setInterval(() => {
            this.topPlugin.update(this.player?.currentTime)
          }, 1000)
        })
        // 暂停
        this.player.on(Events.PAUSE, () => {
          clearInterval(this.timer)
          this.timer = null
        })

        // 进度条拖动事件注册
        this.registerProgressDrag()
      })
    },
    // 注册进度条拖动事件
    registerProgressDrag() {
      // 监听进度条事件
      const progress = this.player.getPlugin('progress')
      this.mobile = this.player.getPlugin('mobile')

      // 进度条开始拖动
      progress.addCallBack('dragstart', (data) => {
        this.isInNodes(data.currentTime)
        // 更新topPlugin
        this.topPlugin.update((data.currentTime))
        clearInterval(this.timer)
        this.timer = null
      })
      // 进度条拖动中
      progress.addCallBack('dragmove', (data) => {
        this.isInNodes(data.currentTime)
        // 更新topPlugin
        this.topPlugin.update((data.currentTime))
      })
      // 进度条点击
      progress.addCallBack('click', (data) => {
        this.isInNodes(data.currentTime)
        // 更新topPlugin
        this.topPlugin.update((data.currentTime))
      })
      // 进度条结束拖动
      progress.addCallBack('dragend', (data) => {
        this.storyNodes.update('')
        // 更新topPlugin
        this.topPlugin.update((data.currentTime))
      })
    },
    // 更新进度条节点
    updateNodes() {
      this.currentVideo.storyNodes.forEach(item => {
        if (this.player.currentTime >= item.time) {
          item.style.backgroundColor = '#42B983'
        } else {
          item.style.backgroundColor = '#F2FEFE'
        }
      })
      this.progresspreview.updateAllDots(this.currentVideo.storyNodes)
    },
    // 判断秒数是否在节点列表内
    isInNodes(time) {
      if (!this.isFullscreen) return;
      // console.log("🚀 ~ isInNodes ~ time:", time)
      // let isInNodes = this.currentVideo.storyNodes && this.currentVideo.storyNodes.find(i => {
      //   console.log("🚀 ~ isInNodes ~ Math.abs(i.time - time) < 2:", Math.abs(i.time - time) < 2,i.time)
      //   return Math.abs(i.time - time) < 2 // 误差范围为4s
      // })
      // let Nodes = this.currentVideo.storyNodes && this.currentVideo.storyNodes.map((i) => Math.abs(i.time - time) )
      let ErrorRange = this.progressTime // 误差范围
      let kingNode = {} // 节点对象
      let beforeVal = ErrorRange // 前一个节点的误差值
      this.currentVideo.storyNodes.forEach(i => {
        let val = Math.abs(i.time - time)
        if (val < ErrorRange && val < beforeVal) {
          kingNode = i
          beforeVal = val
        }
      });
      if (kingNode && kingNode.text) {
        this.storyNodes.update(kingNode.text)
      } else {
        this.storyNodes.update('')
      }
    },
    // 获取当前播放时间
    getCurrentTime() {
      return Math.round(this.player?.currentTime)
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  min-height: 180px;
}

video {
  width: 100% !important;
}

#mse {
  width: 100%;
  height: 100%;
  background: #F3F8FC;
}
</style>