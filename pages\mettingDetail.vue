<template>
  <view class="">
    <view class="container">
      <view class="content">
        <view class="title">{{ detailData.subject || '' }}</view>
        <view class="time">
          <view>
            {{ getDateChinese(Number(detailData.startTime)) }}{{ getWeek(Number(detailData.startTime)) }}
            {{ getHours(Number(detailData.startTime)) + '-' + getHours(Number(detailData.endTime)) }}
          </view>
          <view class="state">
            <view class="icon icon1" v-if="detailData.state == 1">待开始</view>
            <view class="icon icon2" v-if="detailData.state == 2">进行中</view>
            <view class="icon icon3" v-if="detailData.state == 3">已结束</view>
            <view class="icon icon3" v-if="detailData.state == 4">已取消</view>
            <view class="icon icon3" v-if="detailData.state == 5">已销毁</view>
          </view>
        </view>
        <view class="menber flex-start">
          <view class="name">上课人:</view>
          <view class="flex-start">
            <view class="">{{ detailData.userNickName || '' }}</view>
          </view>
        </view>
        <view class="menber flex-start">
          <view class="name">参会人:</view>
          <view class="flex-start">
            <view class="">{{ detailData.studentName || '' }}</view>
          </view>
        </view>
        <view class="btns">
          <button class="entry-b" @click.prevent="openPopup()">
            <view class="entry-m">
              <image src="https://document.dxznjy.com/manage/1718782649000" style="width: 34rpx; height: 34rpx" mode=""></image>
              <view class="marginTop10" style="color: #427ce8">进入会议</view>
            </view>
          </button>
        </view>
      </view>
    </view>

    <u-popup ref="popup" :show="show" mode="center">
      <view class="role_popup">
        <view class="role_title">
          <text>选择角色</text>
        </view>
        <image class="closeImg" @click="closePopup()" src="/static/images/icon_close.png" mode="aspectFill"></image>
        <view class="relo_center" v-if="isMobile">
          <view class="choose_item">
            <view :class="[detailData.studentStatus == 1 ? 'student_status' : hasMeeting1 ? 'student_active' : 'student']" @click="chooseRole(1)">
              学生
              <view class="take_class" v-if="detailData.studentStatus == 1">上课中</view>
            </view>
            <view :class="[hasMeeting2 ? 'student_active' : 'student']" @click="chooseRole(2)">家长</view>
          </view>
        </view>
        <view class="onlyPc" v-if="isPc">
          <image
            src="https://document.dxznjy.com/manage/1721303455000"
            style="width: 34rpx; height: 34rpx; margin-right: 10rpx; vertical-align: middle; margin-bottom: 8rpx"
            mode=""
          ></image>
          为了保证课程流畅进行请使用Chrome浏览器进行上课，
          <a style="text-decoration: none; color: #269662" href="https://chrome-web.com/">点击下载Chrome</a>
        </view>
        <view class="relo_center" v-if="isPc">
          <view class="choose_item">
            <view :class="[detailData.studentStatus == 1 ? 'student_status' : hasMeeting1 ? 'student_active' : 'student']" @click="chooseRole(1)">
              学生
              <view class="take_class" v-if="detailData.studentStatus == 1">上课中</view>
            </view>
            <view :class="[hasMeeting2 ? 'student_active' : 'student']" @click="chooseRole(2)">家长</view>
          </view>
        </view>
        <view class="role_bottom" v-if="isPc && isWechat">
          <view :class="[chooseMeetingRole ? 'left_btn' : 'left_btn_active']" @click="copy()">复制会议链接</view>
          <view class="right_btn" @click="closePopup()">取消</view>
        </view>
        <view class="role_bottom" v-else>
          <view :class="[chooseMeetingRole ? 'left_btn' : 'left_btn_active']" @click="goweburl()">进入会议</view>
          <view class="right_btn" @click="closePopup()">取消</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getMeeting } from '@/api/user.js';
export default {
  data() {
    return {
      id: '',
      type: '1',
      detailData: {},
      isQy: true,
      token: uni.getStorageSync('token'),
      hasMeeting1: false,
      hasMeeting2: false,
      chooseMeetingRole: false,
      url: '',
      meetingId: '',
      show: false,
      isPc: false,
      isMobile: false,
      isWechat: false
    };
  },
  onLoad(e) {
    let that = this;
    if (e.id) {
      that.meetingId = e.id;
      that.initData(e.id);
    }
    wx.getSystemInfo({
      success: function (res) {
        console.log(res);
        if (res.platform === 'android' || res.platform === 'ios') {
          console.log('当前设备是手机');
          that.isMobile = true;
        } else {
          console.log('当前设备是电脑或其他设备');
          that.isPc = true;
        }
      },
      fail: function (err) {
        console.error('获取系统信息失败', err);
      }
    });
    // var ua = window.navigator.userAgent.toLowerCase();
    // var isMobile = /iphone|ipad|ipod|android/.test(ua);

    // if (isMobile) {
    //   // console.log("当前设备是移动端");
    //   alert('当前设备是移动端');
    //   that.isMobile = true;
    // } else {
    //   alert('当前设备是PC端');
    //   that.isPc = true;
    // }
  },
  methods: {
    copy() {
      console.log('123e12');
      let item = this.url;
      if (!this.url) {
        return uni.showToast({
          title: '请选择身份',
          icon: 'none'
        });
      } else {
        uni.setClipboardData({
          data: item,
          success: (res) => {
            console.log(res);
            uni.showToast({
              title: '链接已复制至剪贴板'
            });
          },
          fail: (err) => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      }
    },
    // 打开弹窗
    openPopup() {
      let that = this;
      if (this.detailData.state != 1 && this.detailData.state != 2) {
        let title = this.detailData.state == 3 ? '已结束' : this.detailData.state == 4 ? '已取消' : '已销毁';
        return uni.showToast({
          title: `会议${title}`,
          icon: 'none'
        });
      } else {
        let url = this.detailData.parentsInviteUrl;
        // let userAgent = window.navigator.userAgent;
        if (that.isPc) {
          that.show = true;
          var ua = window.navigator.userAgent.toLowerCase();
          if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
            that.isWechat = true;
          } else if (ua.match(/micromessenger/i) == 'micromessenger') {
            that.isWechat = true;
          }
          console.log('pc');
        } else {
          var ua = window.navigator.userAgent.toLowerCase();
          if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
            console.log('企业微信客户端');
            that.isWechat = true;
            window.location.href = url;
          } else if (ua.match(/micromessenger/i) == 'micromessenger') {
            console.log('微信客户端');
            that.isWechat = true;
            that.initData(that.meetingId);
            that.show = true;
          } else {
            // return console.log(url);
            window.location.href = url;
          }
        }
      }
    },
    // 选择角色
    chooseRole(id) {
      let that = this;
      if (id === 1) {
        if (that.detailData.studentStatus === 1) return;
        that.chooseMeetingRole = true;
        that.hasMeeting1 = true;
        that.hasMeeting2 = false;
        that.url = that.detailData.inviteUrl;
      } else {
        that.chooseMeetingRole = true;
        that.hasMeeting2 = true;
        that.hasMeeting1 = false;
        that.url = that.detailData.parentsInviteUrl;
        console.log(that.url);
      }
    },
    // 跳转外部页面
    goweburl() {
      if (this.chooseMeetingRole) {
        window.location.href = this.url;
      } else {
        return uni.showToast({
          title: '请选择身份',
          icon: 'none'
        });
      }
    },
    // 关闭弹窗
    closePopup() {
      this.hasMeeting1 = false;
      this.hasMeeting2 = false;
      this.chooseMeetingRole = false;
      this.show = false;
    },

    // 日期  -转年月日
    getDateChinese(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适配
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        return `${year}年${month}月${day}日`;
      }
    },
    getWeek(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适
        let week = date.getDay();
        let arr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return `${arr[week]}`;
      }
    },
    getHours(dt) {
      if (!dt) {
        return '';
      } else {
        const date = new Date(dt);
        // let date = new Date(dt.replace(/-/g, '/')); //ios适
        let HH = date.getHours();
        let mm = date.getMinutes();
        HH = HH > 9 ? HH : '0' + HH;
        mm = mm > 9 ? mm : '0' + mm;
        return `${HH}:${mm}`;
      }
    },
    async initData(id) {
      let that = this;
      uni.showLoading({
        title: '加载中'
      });
      let res = await getMeeting({ id });
      uni.hideLoading();
      that.detailData = res;
    }
  }
};
</script>

<style lang="scss">
.flex-start {
  display: flex;
  justify-content: start;
  align-items: center;
}

.container {
  padding-top: 100rpx;
  background-color: #fff;
}

.content {
  // padding-top: 284rpx;
  padding: 36rpx;
  background-color: #fff;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #090909;
  font-family: AlibabaPuHuiTiM;
}

.time {
  display: flex;
  margin-top: 25rpx;
  margin-bottom: 46rpx;
}

.icon {
  min-width: 77rpx;
  height: 36rpx;
  padding: 0 10rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-left: 30rpx;
  color: #fff;
}

.icon1 {
  background-color: #35a8f7;
}

.icon2 {
  background-color: #5cb561;
}

.icon3 {
  background-color: #c2c2c2;
}

.menber {
  margin-bottom: 30rpx;

  .name {
    margin-right: 41rpx;
    font-weight: 700;
  }
}

button {
  all: unset;
}

.shareb {
  position: absolute;
  left: 0;
  width: 379rpx;
  height: 120rpx;
  font-size: 26rpx;
}

.share {
  width: 379rpx;
  height: 120rpx;
  background: #f3f3f3;
  border-radius: 12rpx;
  // margin: 0 auto 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btns {
  position: relative;
  height: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.entry-b {
  // position: absolute;
  // right: 0;
  width: 100%;
  height: 100%;
}

.entry-m {
  width: 100%;
  height: 100%;
  background: #ebf2fd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // margin-left: 22rpx;
  font-size: 26rpx;
}

.entry {
  width: 750rpx;
  height: 96rpx;
  background: #ffffff;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
}

.shareb1 {
  width: 677rpx !important;
  height: 120rpx;
}

.share1 {
  width: 677rpx !important;
  height: 120rpx;
}

.role_popup {
  width: 600rpx;
  min-height: 460rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx 44rpx;
}

.role_title {
  width: 100%;
  height: 90rpx;
  // margin-top: 10rpx;
  letter-spacing: 4rpx;
  font-size: 36rpx;
  color: #090909;
  font-weight: bolder;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeImg {
  width: 42rpx;
  height: 42rpx;
  position: absolute;
  right: 40rpx;
  top: 30rpx;
}

.relo_center {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.onlyPc {
  font-size: 28rpx;
  line-height: 40rpx;
}

.choose_item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  width: 100%;
  height: 240rpx;
}

.student_status {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #f2f2f2;
  font-size: 32rpx;
  color: #b0b0b0;
  background-color: #f7f7f7;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.student {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #c8c8c8;
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.student_active {
  width: 100%;
  height: 40px;
  border-radius: 10rpx;
  border: 1rpx solid #2e896f;
  font-size: 32rpx;
  color: #2e896f;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.take_class {
  position: absolute;
  right: 50rpx;
  width: 120rpx;
  height: 58rpx;
  background: #35a8f7;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #fff;
}

.role_bottom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  // margin-top: 40rpx;
}

.left_btn {
  width: 220rpx;
  height: 60rpx;
  color: #fff;
  background-color: #2e896f;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_btn_active {
  width: 220rpx;
  height: 60rpx;
  border: 1rpx solid #f2f2f2;
  color: #b0b0b0;
  background-color: #f7f7f7;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right_btn {
  width: 220rpx;
  height: 60rpx;
  color: #2e896f;
  border: 1rpx solid #2e896f;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
