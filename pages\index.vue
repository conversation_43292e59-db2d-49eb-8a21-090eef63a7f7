<template>
  <view class="content">
    <view class="container" :style="{ height: useHeight + 'rpx' }">
      <image class="login_img" mode="widthFix" src="https://document.dxznjy.com/app/images/zhujiaoduan/logo.png"></image>
    </view>
  </view>
</template>

<script>
import { getTicket } from '@/api/user.js';
import setting from '@/common/config.js';
import sha1 from 'js-sha1';
export default {
  data() {
    return {
      title: 'Hello',
      env: null,
      path: '/qyWechat/takingOrder',
      // path: '',
      useHeight: 0,
      Url: '',
      qyConfig: {
        corpid: 'ww48ea1bc9b2477b69', //线上
        agentid: 1000014, //线上
        appid: 'wxce2bd1113a024ff6' //线上
      }
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(e) {
    console.log(setting);
    this.qyConfig = setting.qyConfig ? setting.qyConfig : this.qyConfig;
    console.log(this.qyConfig, '1111111111111111111111');
    let systemInfo = uni.getSystemInfoSync();
    let platform = systemInfo.platform;
    if (platform == 'android') {
      // 安卓系统的处理逻辑
      console.log('Android系统');
      // Vue.prototype.sdk = jWeixin
      this.env = jWeixin;
    } else if (platform == 'ios') {
      // Vue.prototype.sdk = wx
      this.env = wx;
      // iOS系统的处理逻辑
      // console.log('iOS系统');
    }
    let isQy = window.navigator.userAgent;
    var reg = RegExp(/wxwork/);
    // isQy.match()
    // console.log(reg.test(isQy));

    // this.title = reg.test(isQy) ? '企微' : '微信';
    // console.log(window.navigator.userAgent);
    let url = window.location.href;
    var parts = url.split('#');
    let newUrl = parts[0];
    this.Url = newUrl;
    setTimeout(() => {
      this.jumpMin();
    }, 500);
  },
  onShow() {
    document.addEventListener('visibilitychange', function () {
      if (document.hidden) {
        console.log('hidden');
      } else {
        window.close();
        // 此处填写H5监听到关闭小程序的操作，比如wx.closeWindow();
        console.log('监听到关闭小程序');
      }
    });
  },
  methods: {
    async jumpMin() {
      let that = this;
      uni.showLoading({ title: '跳转中' });
      let tokenTicket = await that.getAccessTokenAndTicket();
      // console.log(tokenTicket, '=============');
      // this.title = tokenTicket;
      let ticket = tokenTicket.agentTicket; //有效期2小时

      let obj = {
        jsapi_ticket: ticket, //应用ticketg
        nonceStr: '10014', //随机字符串, wx.agentConfig内的nonceStr值要与此值一致
        timestamp: parseInt(Date.now() / 1000), //时间戳, wx.agentConfig内的timestamp值要与此值一致
        url: that.Url //当前网页的url
      };

      let signature = await that.getSignature(obj); //签名权限
      await that.env.agentConfig({
        // corpid: 'ww48ea1bc9b2477b69', // 必填，企业微信的corpid，必须与当前登录的企业一致
        // corpid: 'wwcc79054d80e112df', // 必填，企业微信的corpid，必须与当前登录的企业一致
        corpid: that.qyConfig.corpid, // 必填，企业微信的corpid，必须与当前登录的企业一致
        agentid: that.qyConfig.agentid, // 必填，企业微信的应用id
        // agentid: 1000014, // 必填，企业微信的应用id
        // agentid: 1000040, // 必填，企业微信的应用id
        timestamp: obj.timestamp, // 必填，生成签名的时间戳
        nonceStr: obj.nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ['launchMiniprogram'], //必填
        success: function (res) {
          // uni.showToast({
          //   title: 'agentConfig成功回调'
          // });
          that.env.invoke(
            'launchMiniprogram',
            {
              appid: that.qyConfig.appid,
              // appid: 'wxce2bd1113a024ff6', // 需跳转的小程序appid
              // appid: 'wx89164566cfdabfdf', // 需跳转的小程序appid
              path: that.path // 所需跳转的小程序内页面路径及参数。非必填
            },
            function (res) {
              console.log(res);
              if (res.err_msg == 'launchMiniprogram:ok') {
                // 正常
                uni.hideLoading();
                uni.showToast({
                  title: '跳转成功',
                  icon: 'none'
                });
                // window.close();
                // 关闭中间的H5页面
                that.env.miniProgram.navigateBack({});
              } else {
                uni.hideLoading();
                // 错误处理
                uni.showModal({
                  title: '温馨提示',
                  content: res.errMsg + '跳转失败',
                  showCancel: false
                });
              }
            }
          );
        },
        fail: function (res) {
          uni.hideLoading();
          uni.showModal({
            title: '温馨提示',
            content: res.errMsg + '企业微信注入失败',
            showCancel: false
          });
          if (res.errMsg.indexOf('function not exist') > -1) {
            alert('版本过低请升级');
          }
        }
      });
    },
    getSignature(obj) {
      let sign = `jsapi_ticket=${obj.jsapi_ticket}&noncestr=${obj.nonceStr}&timestamp=${obj.timestamp}&url=${obj.url}`;
      return sha1(sign);
    },
    async getAccessTokenAndTicket() {
      let res = await getTicket({
        url: this.Url
      });
      return res;
    }
    /*
    async jumpMin() {
      let that = this;
      uni.showLoading({
        title: '跳转中'
      });
      let tokenTicket = await this.getAccessTokenAndTicket();
      // let corpId = 'ww48ea1bc9b2477b69'; //企业ID
      // let agentId = '1000014'; //应用ID, agentId, 必须是整型
      // console.log(tokenTicket);
      // let appSecret = 'LrGC2tCubshio6TxWPnO_OriggGohO-oKwzAbJsFetQ'; // 应用secret
      // let token = tokenTicket.token; //有效期2小时
      let ticket = tokenTicket.agentTicket; //有效期2小时
      // let timeStamp = Date.now();
      // let timeStamp = parseInt(Date.now() / 1000);
      let obj = {
        jsapi_ticket: ticket, //应用ticketg
        nonceStr: '10014', //随机字符串, wx.agentConfig内的nonceStr值要与此值一致
        timestamp: parseInt(Date.now() / 1000), //时间戳, wx.agentConfig内的timestamp值要与此值一致
        url: window.location.href //当前网页的url
      };
      // console.log(obj);
      let signature = this.getSignature(obj); //签名权限
      // console.log(signature, '==================');
      // jWeixin.config({
      //   beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      //   debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      //   appId: 'ww48ea1bc9b2477b69', // 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用
      //   timestamp: obj.timestamp, // 必填，生成签名的时间戳
      //   nonceStr: obj.nonceStr, // 必填，生成签名的随机串
      //   signature: signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
      //   jsApiList: ['launchMiniprogram'] //必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
      // });
      // console.log(jWeixin);
      // let daa = {
      //   corpid: 'ww48ea1bc9b2477b69', // 必填，企业微信的corpid，必须与当前登录的企业一致
      //   agentid: 1000014, // 必填，企业微信的应用id
      //   timestamp: obj.timestamp, // 必填，生成签名的时间戳
      //   nonceStr: obj.nonceStr, // 必填，生成签名的随机串
      //   signature: signature // 必填，签名，见附录-JS-SDK使用权限
      // };
      // console.log(daa, '-----------');
      that.env.agentConfig({
        corpid: 'ww48ea1bc9b2477b69', // 必填，企业微信的corpid，必须与当前登录的企业一致
        agentid: 1000014, // 必填，企业微信的应用id
        timestamp: obj.timestamp, // 必填，生成签名的时间戳
        nonceStr: obj.nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ['launchMiniprogram'], //必填
        success: function (res) {
          uni.showToast({
            title: 'agentConfig成功回调'
          });
          that.env.invoke(
            'launchMiniprogram',
            {
              appid: 'wxce2bd1113a024ff6', // 需跳转的小程序appid
              // path: '/qyWechat/takingOrder' // 所需跳转的小程序内页面路径及参数。非必填
              path: that.path // 所需跳转的小程序内页面路径及参数。非必填
            },
            function (res) {
              console.log(res);
              if (res.err_msg == 'launchMiniprogram:ok') {
                // 正常
                uni.showToast({
                  title: '跳转成功',
                  icon: 'none'
                });
                // window.close();
                uni.hideLoading();
                // that.env.closeWindow(); // 关闭中间的H5页面
              } else {
                uni.hideLoading();
                // 错误处理
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                });
              }
            }
          );
        },
        fail: function (res) {
          // console.log(res, 'agentConfig失败回调');
          uni.hideLoading();
          uni.showModal({
            content: res.errMsg + 'agentConfig失败回调'
          });
          if (res.errMsg.indexOf('function not exist') > -1) {
            alert('版本过低请升级');
          }
        }
      });
    },
    */
  }
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}
.container {
  text-align: center;
  background-color: #fff;
  padding-top: 150rpx;
}

.login_img {
  width: 300rpx;
  margin-top: 150rpx;
}
</style>
