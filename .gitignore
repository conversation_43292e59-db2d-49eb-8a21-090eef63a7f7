# 依赖目录
node_modules/
.node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建产物
unpackage/dist/
dist/
build/

# 环境配置文件
.env
.env.local
.env.*.local

# 日志文件
logs/
*.log

# 运行时文件
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.eslintcache

# 操作系统
.DS_Store
**/.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.cache/

# HBuilderX相关
.hbuilderx/

# 微信小程序
project.config.json

# 构建脚本产生的文件
build-*.tar.gz

# 测试文件
test/coverage/

# Python
*.pyc

# 其他
.sass-cache/
