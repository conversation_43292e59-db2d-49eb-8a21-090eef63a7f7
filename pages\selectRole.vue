<template>
  <view>
    <view class="container" :style="{ height: useHeight + 'rpx' }">
      <view class="dialog-box">
        <view
          class="dialog-item input-border"
          @click="chooseStudentlist(item, index)"
          :key="index"
          v-for="(item, index) in roleArr"
          :class="isactive == index ? 'selected' : 'not-selected'"
        >
          {{ item.name }}
        </view>
      </view>
      <view class="fixBottom">
        <view class="button" @click="onSure">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getTicket, selectRole, getMinUrl } from '@/api/user.js';
  import sha1 from 'js-sha1';
  export default {
    data() {
      return {
        useHeight: 0,
        title: 'Hello',
        env: null,
        // path: '/qyWechat/takingOrder'
        path: '',
        role: '',
        id: '',
        isactive: -1,
        roleArr: [
          { name: '家长', value: 1 },
          { name: '推荐人', value: 2 },
          { name: '小组组长', value: 3 },
          { name: '教练', value: 4 }
        ],
        Url: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          // console.log(res);
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onLoad(e) {
      let systemInfo = uni.getSystemInfoSync();
      let platform = systemInfo.platform;
      if (platform == 'android') {
        this.env = jWeixin;
      } else if (platform == 'ios') {
        this.env = wx;
      }
      let url = window.location.href;
      var parts = url.split('#');
      let newUrl = parts[0];
      this.Url = newUrl;
      if (e.id) {
        this.id = e.id;
      }
    },
    onShow() {
      document.addEventListener('visibilitychange', function () {
        if (document.hidden) {
          console.log('hidden');
        } else {
          window.close();
          // 此处填写H5监听到关闭小程序的操作，比如wx.closeWindow();
          console.log('监听到关闭小程序');
        }
      });
    },
    methods: {
      async onSure() {
        // console.log(this.role);
        let that = this;
        let userAgent = window.navigator.userAgent;
        var reg = RegExp(/wxwork/);
        var isQy = reg.test(userAgent);
        if (!that.role) {
          return uni.showToast({
            title: '请选择身份',
            icon: 'none'
          });
        }
        if (that.role == 1) {
          // 上线后解开注释
          // let url = await getMinUrl({
          //   path: 'qyLogin/login'
          // });
          if (isQy) {
            uni.showToast({
              title: '家长只能在微信中选择'
            });
          } else {
            let res = await selectRole({
              type: 1,
              id: that.id
            });
            if (!res) {
              uni.showToast({
                title: '选择成功'
              });
              setTimeout(() => {
                // window.close();
                WeixinJSBridge.call('closeWindow');
                // wx.closeWindow();
              }, 1000);
            } else {
              // window.location.href = url;
              // console.log(res);
              let code = encodeURIComponent(JSON.stringify(res));
              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/qrCode?code=${code}`
                });
              }, 500);
            }
          }
        } else {
          let res = await selectRole({
            type: that.role,
            id: that.id
            // id: '1252263917364191232'
          });
          console.log(res, '===========================');
          if (!res) {
            uni.showToast({
              title: '选择成功'
            });
            setTimeout(() => {
              window.close();
            }, 1000);
          } else {
            // console.log(res, '======================');
            // let code = res;
            let code = encodeURIComponent(JSON.stringify(res));
            // let code = 'https://document.dxznjy.com/manage/1719221853000';
            // console.log(code, '====================');
            let path = `/qyWechat/qrCode.html?qrCode=${code}`;
            that.jumpMin(path);
          }
        }
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.role = item.value;
      },
      async jumpMin(path) {
        let that = this;
        uni.showLoading({ title: '跳转中' });
        let tokenTicket = await that.getAccessTokenAndTicket();
        let ticket = tokenTicket.agentTicket; //有效期2小时
        let obj = {
          jsapi_ticket: ticket, //应用ticketg
          nonceStr: '10014', //随机字符串, wx.agentConfig内的nonceStr值要与此值一致
          timestamp: parseInt(Date.now() / 1000), //时间戳, wx.agentConfig内的timestamp值要与此值一致
          url: that.Url //当前网页的url
        };
        let signature = await that.getSignature(obj); //签名权限
        await that.env.agentConfig({
          corpid: 'ww48ea1bc9b2477b69', // 必填，企业微信的corpid，必须与当前登录的企业一致
          // corpid: 'wwcc79054d80e112df', // 必填，企业微信的corpid，必须与当前登录的企业一致
          // agentid: 1000040, // 必填，企业微信的应用id
          agentid: 1000014, // 必填，企业微信的应用id
          timestamp: obj.timestamp, // 必填，生成签名的时间戳
          nonceStr: obj.nonceStr, // 必填，生成签名的随机串
          signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
          jsApiList: ['launchMiniprogram'], //必填
          success: function (res) {
            // uni.showToast({
            //   title: 'agentConfig成功回调'
            // });
            that.env.invoke(
              'launchMiniprogram',
              {
                appid: 'wxce2bd1113a024ff6', // 需跳转的小程序appid
                // appid: 'wx89164566cfdabfdf', // 需跳转的小程序appid
                path: path // 所需跳转的小程序内页面路径及参数。非必填
              },
              function (res) {
                if (res.err_msg == 'launchMiniprogram:ok') {
                  // 正常
                  uni.hideLoading();
                  uni.showToast({
                    title: '跳转成功',
                    icon: 'none'
                  });
                  // window.close();
                  // 关闭中间的H5页面
                  that.env.miniProgram.navigateBack({});
                } else {
                  uni.hideLoading();
                  // 错误处理
                  uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  });
                }
              }
            );
          },
          fail: function (res) {
            // console.log(res, 'agentConfig失败回调');
            uni.showToast({
              title: res.errMsg + 'agentConfig失败回调'
            });
            uni.hideLoading();
            if (res.errMsg.indexOf('function not exist') > -1) {
              alert('版本过低请升级');
            }
          }
        });
      },
      getSignature(obj) {
        let sign = `jsapi_ticket=${obj.jsapi_ticket}&noncestr=${obj.nonceStr}&timestamp=${obj.timestamp}&url=${obj.url}`;
        return sha1(sign);
      },
      async getAccessTokenAndTicket() {
        let res = await getTicket({
          url: this.Url
        });
        return res;
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f3f8fc;
  }
  .container {
    text-align: center;
    background-color: #fff;
    border-radius: 15rpx;
    // padding-top: 150rpx;
  }

  .dialog-box {
    padding: 150rpx 20rpx 0 20rpx;
  }
  .dialog-item {
    width: 640rpx;
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }
  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }
  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }
  .fixBottom {
    position: fixed;
    bottom: 50rpx;
    width: 100%;
  }
  .button {
    margin: 0 auto;
    width: 586rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    color: #fff;
    background: #2e896f;
    border-radius: 45rpx;
  }
</style>
