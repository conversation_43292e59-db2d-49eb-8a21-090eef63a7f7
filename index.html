<!DOCTYPE html>
<html lang="en">

<head>
  <style>
    /* 西瓜视频播放器样式 */
    .xgplayer video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      outline: none;
    }
  </style>
  <meta charset="UTF-8" />
  <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vConsole/3.9.1/vconsole.min.js"></script> -->
  <script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js" referrerpolicy="orgin"></script>
  <!-- <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js" referrerpolicy="orgin"></script>   -->
  <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
  <script src="https://res.wx.qq.com/wwopen/js/jsapi/jweixin-1.0.0.js"></script>
  <!-- <script src="https://player.polyv.net/resp/vod-player/latest/player.js"></script> -->
  <!-- <script src="https://res.wx.qq.com/wwopen/js/jsapi/jweixin-1.0.0.js" referrerpolicy="orgin"></script> -->
  <script>
    // 解决viewport适配问题
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    // 保存token到localStorage
    const getQueryString = (name) => {
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
      const search = window.location.hash.split('?')[1] || '';
      const r = search.match(reg) || [];
      return r[2];
    };
    let token = getQueryString('token') || '';
    localStorage.setItem('token', token)
  </script>
  <title></title>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app"><!--app-html--></div>
  <!-- <script type="module" src="/qyWechat/main.js"></script> -->
</body>

</html>