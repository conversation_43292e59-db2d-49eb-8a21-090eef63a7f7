<template>
    <div class="play-video">
        <view class="flex-a-c nav-bar plr-20" v-if="isIos && !noNavbar">
            <uni-icons type="left" size="24" @click="returnBack"></uni-icons>
            <view class="nav_title f-28">学习视频</view>
        </view>
        <div class="ios-cont" v-if="isIos && !noNavbar"></div>
        <div class="app-container">
            <!-- 视频播放区域 -->
            <xgPlayer ref="XgPlayer" :currentVideo="currentVideo" :isIos="isIos" :isRotate="noNavbar" class="video-player"></xgPlayer>
            <!-- 视频选择区域 -->
            <div class="video-select">
                <h3>选择你的学习视频：</h3>
                <u-collapse :value="[index0]" :border="false" class="collapse">
                    <u-collapse-item :title="item.courseName" :name="index" border v-for="(item, index) in videoList" :key="item.courseId">
                        <div class="video-list">
                            <div
                                @click="changeVideo(index, index2)"
                                :class="['video-item', { active: index === currentIndex[0] && index2 === currentIndex[1] }]"
                                v-for="(item2, index2) in item.videoList"
                                :key="index2"
                            >
                                <image
                                    v-if="index === currentIndex[0] && index2 === currentIndex[1]"
                                    class="image_icon--play"
                                    style="width: 24rpx; height: 22rpx; flex-shrink: 0"
                                    src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"
                                ></image>
                                <image
                                    v-else
                                    mode="widthFix"
                                    class="image_icon"
                                    style="width: 24rpx; height: 22rpx; flex-shrink: 0"
                                    src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"
                                ></image>
                                <div>{{ item2.videoName }}</div>
                            </div>
                        </div>
                    </u-collapse-item>
                </u-collapse>
            </div>
            <!-- 点击保存学习记录 -->
            <!-- <div class="ptb-20">
      <u-button v-if="videoList && videoList.length >0"  @click="saveStudyRecord(true)" icon="checkbox-mark" iconColor="#fff" class="btn-color">保存学习记录</u-button>
    </div> -->
            <u-loading-page loading-mode="circle" :loading="loading" loadingColor="#489981"></u-loading-page>
        </div>
    </div>
</template>

<script>
import xgPlayer from './components/xgPlayer/xgPlayer.vue';
import { getVideoInfo, getVideoNode, getVideoList, getStudyRecord, saveStudyRecord } from '../../api/zxPlayVideo';

// console.log('webUni', webUni);

export default {
    data() {
        return {
            loading: true, // 加载中
            videoInfo: null, // 视频信息
            index0: 0, // 记录播放节点索引
            currentIndex: [], // 当前播放的节点索引
            currentVideo: null, // 当前播放的视频
            videoList: [], // 视频列表
            startTime: 0, // 播放开始时间
            playTime: 0, // 记录播放时间
            storyNodes: [], // 故事节点
            isIos: false, // 是否是ios
            noNavbar: false // 是否隐藏导航栏
        };
    },
    components: {
        xgPlayer
    },
    mounted() {
        // window.$saveStudyRecord = this.saveStudyRecord
        // this.changeVideo(0)
        // this.getVideoList()
        // document.addEventListener('visibilitychange', () => {
        //   if (document.visibilityState === 'hidden') {
        //     console.log("页面被隐藏，保存记录");
        //     this.saveStudyRecord();
        //   }
        // });
        setInterval(() => {
            this.saveStudyRecord();
        }, 15000);
    },
    onLoad(options) {
        this.isIos = options.isIos == 1; // 是否是ios
        this.noNavbar = options.noNavbar == 1; // 是否隐藏导航栏
        // 视频信息
        this.videoInfo = JSON.parse(options.dataInfo);
        console.log('🚀 ~ onLoad ~  this.videoInfo:', this.videoInfo);
        // // 解码
        // // 先还原base64字符串
        // let base64 = options.sendData.replace(/-/g, '+').replace(/_/g, '/');
        // // 补足等号
        // while (base64.length % 4) {
        //   base64 += '=';
        // }
        // let jsonString = decodeURIComponent(escape(atob(base64)));
        // let data = JSON.parse(jsonString);
        // console.log('🚀 ~ onLoad ~ data:', data);
        // this.videoList = data; // 视频列表
        // this.changeVideo(0,0)
        this.getVideoList();
        // console.log("🚀 ~ onLoad ~ this.detectDevice():", this.detectDevice())
    },
    beforeDestroy() {
        this.saveStudyRecord();
    },
    methods: {
        // 获取学习视频资料
        async getVideoList() {
            // 获取学习视频资料
            const data =  await getVideoList(this.videoInfo )
            this.videoList = data;

            let res = this.videoList[0].learnRecord;
            // 如果没有上次的学习记录，则直接退出
            if (!res) {
                return;
            }

            // 有则继续播放上次的学习记录
            // 获取播放时间
            let s = res.playTime.split(':').reduce((a, b) => a * 60 + +b);
            this.startTime = isNaN(s) ? 0 : s; // 记录播放开始时间
            if(this.startTime != 0)  this.playTime = this.startTime; // 记录播放时间
            // console.log("🚀 ~ getVideoList ~ this.startTime:", this.startTime)
            // console.log("🚀 ~ getVideoInfo ~ res:", res.courseId)
            // 获取播放节点索引
            // 大列的索引
            let index0 = this.videoList.findIndex((i) => i.versionId == res.versionId); // 记录播放节点索引
            if (index0 == -1) index0 = 0;
            this.index0 = index0;
            // 小列的索引
            let index = this.videoList[index0].videoList.findIndex((i) => i.videoId == res.courseVideoId); // 记录播放节点索引
            if (index == -1) index = 0;
            // 切换到上次的视频
            this.changeVideo(index0, index);
            // console.log("🚀 ~ getVideoList ~ index0, index:", index0, index)
        },
        // 切换视频
        changeVideo(index, index2) {
            // 修改当前播放的视频
            this.currentIndex = [index, index2];
            // this.currentVideo = this.videoList[0].list[index];
            this.getVideoInfo(this.videoList[index].videoList[index2]);
        },
        // 获取视频信息
        async getVideoInfo(item) {
            // console.log("🚀 ~ getVideoInfo ~ item:", item)
            try {
                let { data } = await getVideoInfo({ vid: item.videoVid }, !this.loading);
                let list = data.data[0];
                // console.log("🚀 ~ getVideoInfo ~ list.basicInfo.coverURL:", list.basicInfo.coverURL)
                // 视频清晰度列表
                let transcodeInfos = list.transcodeInfos.map((i) => {
                    return {
                        name: i.definition == 'LD' ? '标清' : i.definition == 'SD' ? '高清' : i.definition == 'HD' ? '超清' : i.definition,
                        definition: i.playUrl,
                        url: i.playUrl
                    };
                });
                // 视频节点列表
                let data2 = await getVideoNode({ id: item.videoId }, !this.loading);
                // console.log("🚀 ~ getVideoInfo ~ data2:", data2)
                let nodes = data2.nodes.map((i, index) => {
                    return {
                        id: index, // 唯一标识，用于删除的时候索引
                        time: i.nodeTime.split(':').reduce((a, b) => a * 60 + +b), // 展示的时间点，例子为在播放到10s钟的时候展示
                        text: i.nodeName, // hover的时候展示文案，可以为空
                        duration: 5, // 展示时间跨度，单位为s
                        style: {
                            // 指定样式
                            backgroundColor: '#F2FEFE'
                        }
                    };
                });
                this.storyNodes = nodes;
                // 视频信息
                this.currentVideo = {
                    ...item,
                    cover: list.basicInfo.coverURL, // 封面
                    url: transcodeInfos[transcodeInfos.length - 1].url,
                    transcodeInfos,
                    startTime: this.startTime,
                    storyNodes: this.storyNodes
                };
                // console.log("🚀 ~ getVideoInfo ~ this.currentVideo:", this.currentVideo)
                // 保存记录
                if (!this.loading) this.saveStudyRecord(false, true);
                this.loading = false; // 加载完成
            } catch (error) {
                this.currentVideo = {
                    ...item,
                    url: 'https://v.polyv.net/uc/video/getMp4?vid=' + item.videoVid,
                    transcodeInfos: [],
                    storyNodes: []
                };
                this.loading = false; // 加载完成
            }
            // console.log("🚀 ~ getVideoInfo ~ res:", data.data[0])
        },
        // 保存学习记录
        /**
         *
         * @param isTip 是否提示
         * @param isSave 是否强制保存
         */
        async saveStudyRecord(isTip = false, isSave = false) {
            // 记录播放时间
            let playTime = this.$refs?.XgPlayer.getCurrentTime() || 0;
            if (playTime == this.playTime && !isSave) {
                if (isTip) uni.showToast({ title: '学习记录已保存', icon: 'none' });
                return;
            }
            this.playTime = playTime; // 记录播放时间
            await saveStudyRecord({
                ...this.videoInfo,
                versionId: this.videoList[this.currentIndex[0]].versionId,
                courseVideoId: this.currentVideo?.videoId,
                playTime
            });
            if (isTip) uni.showToast({ title: '学习记录已保存', icon: 'none' });
            // console.log("🚀 ~ saveStudyRecord ~ playTime:", playTime)
        },
        // 设备类型判断
        detectDevice() {
            var userAgent = navigator.userAgent || navigator.vendor || window.opera;

            // iOS detection:
            if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                return 'iOS';
            }

            // Android detection:
            if (/Android/.test(userAgent)) {
                return 'Android';
            }

            // Default to other, or no device detected.
            return 'Other';
        },
        // 返回上一页
        returnBack() {
            webUni.navigateBack();
        }
    }
};
</script>
<style lang="scss" scoped>
/* 定义全局样式变量 */
:root {
    --primary-color: #489981;
    /* 主色调 - 青绿色 */
    --hover-color: #f1f5f9;
    /* 悬停背景色 */
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    /* 阴影 */
    --radius: 12px;
    /* 圆角 */
    --transition: all 0.3s ease;
    /* 过渡效果 */
}

.play-video {
    height: 100vh;
}
.ios-cont {
    margin-top: 150rpx;
}
.app-container {
    padding: 20rpx;
    box-sizing: border-box;
    background: #f3f8fc;
    color: #333;
}

::v-deep {
    .u-collapse-item__content__text {
        padding-top: 0;
    }

    .u-cell__title-text {
        font-weight: 600;
        color: --primary-color;
    }

    .u-collapse-item {
        border-top: 1px solid #ddd;
        background: #fff;
    }

    .u-loading-page {
        z-index: 999999;
    }
}

.ptb-20 {
    padding: 20rpx 0;
}
.flex-a-c {
    display: flex;
    align-items: center;
}
.plr-20 {
    padding: 0 20rpx;
}

// .collapse {
//   border-radius: 10rpx;
//   background: #fff;
// }
// title
.nav-bar {
    position: fixed;
    top: 0;
    background-color: #f3f8fc;
    width: 100%;
    height: 100rpx;
    padding-top: 50rpx;
    z-index: 9;
}

.nav_title {
    margin-left: 32%;
}

// 视频播放区域样式
.video-player {
    border-radius: 10rpx 10rpx 0 0;
    overflow: hidden;
    box-sizing: border-box;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 视频选择区域样式
.video-select {
    border-bottom-right-radius: 15rpx;
    border-bottom-left-radius: 15rpx;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* 视频选择区域标题 */
    h3 {
        margin: 0;
        padding: 15px;
        background-color: #fff;
        // border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        font-weight: 600;
    }
}

// 视频列表容器
.video-list {
    // 单个选择视频项样式
    .video-item {
        min-height: 40rpx;
        display: flex;
        align-items: center;
        padding: 20rpx;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 1px solid #eee;

        &.active {
            border-radius: 10rpx;
            font-weight: 600;
            background-color: rgba(72, 153, 129, 0.05);
            overflow: hidden;
        }

        // 单个选择视频项前端icon
        .image_icon {
            &--play {
                width: 24rpx;
                height: 22rpx;
                margin-right: 16rpx;
            }

            width: 24rpx;
            height: 10rpx;
            margin-right: 16rpx;
        }
    }
}

// 保存记录按钮
.btn-color {
    border-radius: 10rpx;
    font-size: 30rpx;
    color: #ffffff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
}
</style>
