<template>
  <view style="padding: 20rpx 10rpx">
    <view v-if="data.length > 0">
      <view v-for="(e, i) in data" :key="i" @click="gotoPaper(e)">
        <view class="listStyle">
          <view class="ellipsis-text">{{ e.testPaperName }}</view>
          <view v-if="e.examStatus == 0 || e.examStatus == 2">
            <view class="greyBgBtn">{{ getBtnName(e) }}</view>
          </view>
          <view v-else-if="e.examStatus == 1">
            <view class="greenBorderBtn">{{ getBtnName(e) }}</view>
          </view>
          <view v-else-if="e.examStatus == 3">
            <view class="greenBgBtn">{{ getBtnName(e) }}</view>
          </view>
        </view>
        <view v-if="i != data.length - 1" class="line"></view>
      </view>
    </view>
    <view v-else style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh">
      <image src="../../static/image/icon_null.png" style="width: 120rpx; height: 130rpx"></image>
      <view style="margin: 20rpx 0">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import { getStudentExamResults } from '@/api/mathReq.js';
  export default {
    data() {
      return {
        data: [],
        studentCode: '',
        studyId: '',
        uuid: ''
      };
    },
    onLoad(e) {
      this.studentCode = e.studentCode ?? '';
      this.studyId = e.studyId ?? '';
      this.uuid = e.uuid ?? '';
      if (this.uuid) {
        uni.setStorageSync('token', this.uuid);
        // this.getToken();
      }
      this.init();
    },
    methods: {
      getBtnName(data) {
        if (data.examStatus == 0) {
          return '未开始';
        } else if (data.examStatus == 1) {
          return '继续答题';
        } else if (data.examStatus == 2) {
          return '已中止';
        } else if (data.examStatus == 3) {
          return '查看结果';
        }
      },
      async init() {
        let params = {
          studentCode: this.studentCode,
          studyId: this.studyId
        };
        let res = await getStudentExamResults(params);
        if (res) {
          this.data = res;
        }
      },
      gotoPaper(data) {
        if (data.examStatus == 0 || data.examStatus == 2) {
          return;
        }
        var questionId = '';
        if (data.questionId != null && data.questionId != undefined) {
          questionId = data.questionId;
        }
        var stageNumber = 0;
        if (data.stageNumber != null && data.stageNumber != undefined) {
          stageNumber = data.stageNumber;
        }
        //试卷需要数据
        // studyId studentCode  classType courseId testPaperCategory questionId stageNumber uuid(token)
        let params = `studyId=${this.studyId}&studentCode=${this.studentCode}&classType=${data.classType}&courseId=${data.courseId}&testPaperCategory=${data.testPaperCategory}&questionId=${questionId}&stageNumber=${stageNumber}&uuid=${this.uuid}`;
        uni.navigateTo({
          url: `/pages/math/answerSheet?${params}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .listStyle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    height: 80rpx;
  }
  .greenBgBtn {
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 60rpx;
    text-align: center;
    font-size: 28rpx;
    background-color: #428a6f;
    color: #fff;
    width: 180rpx;
  }
  .greenBorderBtn {
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 60rpx;
    text-align: center;
    font-size: 28rpx;
    border: 1rpx solid #428a6f;
    color: #428a6f;
    width: 180rpx;
  }
  .greyBgBtn {
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 60rpx;
    text-align: center;
    font-size: 28rpx;
    background-color: #9c9c9c;
    color: #fff;
    width: 180rpx;
  }
  .ellipsis-text {
    width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 限制为1行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  .line {
    height: 2rpx;
    width: 100%;
    margin: 20rpx auto;
    background-color: #dddddd;
  }
</style>
