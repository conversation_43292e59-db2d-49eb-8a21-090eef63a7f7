<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="container" v-if="data">
    <view class="testName">
      <image src="https://document.dxznjy.com/dxSelect/ceb7c256-3d92-4ff4-84b1-da01280b0535.png" style="width: 38rpx; height: 40rpx"></image>
      <view class="testNameText">{{ data && data.testPaperName }}</view>
    </view>
    <view class="criterion">
      <view class="criterionTop" v-if="testPaperCategory != 2">
        <view style="flex: 1; display: flex; padding-left: 35rpx">
          <image src="https://document.dxznjy.com/dxSelect/0b6b594e-fdb3-448b-9c1c-631fece9a731.png" style="width: 40rpx; height: 40rpx" mode=""></image>
          <view class="criterionText" style="margin-left: 12rpx" v-if="status == 2">
            得分{{ data.studentScore }}分
            <uni-icons v-if="isShowRefresh" @click="refreshBtn()" style="margin-left: 20rpx" type="refreshempty" size="18" color="#49A784"></uni-icons>
          </view>
          <view class="criterionText" style="margin-left: 12rpx" v-else>总分{{ data && data.score }}分</view>
        </view>
        <view style="flex: 1; display: flex">
          <image src="https://document.dxznjy.com/dxSelect/35f6ac7e-a7ad-467f-a4c2-7430e4d97186.png" style="width: 40rpx; height: 40rpx" mode=""></image>
          <view class="criterionText" style="margin: 0 12rpx">
            <span v-if="status == 1 || status == 3">剩余时间{{ examTime }}</span>
            <span v-if="status == 2">用时{{ data && getFormotTime(data.examDuration) }}</span>
          </view>
        </view>
      </view>
      <view v-if="isShowPK" :class="isWin ? 'parentPKWinImg' : 'parentPKFailImg'">
        <image v-if="isWin" src="https://document.dxznjy.com/dxSelect/icon_win.png" style="width: 104rpx; height: 53rpx" mode=""></image>
        <view class="avatar-wrapper" style="margin: 0 10rpx">
          <image v-if="isWin" class="crown-icon" src="https://document.dxznjy.com/dxSelect/icon_king.png" mode="aspectFit"></image>
          <view :class="isWin ? 'avatar-win-box' : 'avatar-fail-box'">
            <text :class="isWin ? 'avatar-win-text' : 'avatar-fail-text'">{{ (data.userName + '').substring(0, 1) }}</text>
          </view>
        </view>
        <span :class="isWin ? 'avatar-win-name' : 'avatar-fail-name'">{{ data.userName }}</span>
        <span style="font-weight: bold; color: #fff; font-size: 50rpx; margin-left: 15rpx">{{ data.studentScore }}</span>
      </view>
      <view v-if="testPaperCategory != 2" class="line"></view>
      <view class="criterionBtns" v-if="status == 1 || status == 3">
        <view class="criterionBtn" @click="last" :style="{ visibility: index == 0 ? 'hidden' : '' }">上一题</view>
        <view style="font-size: 30rpx; font-weight: bold; display: flex; align-items: center" @click="openOptions">
          {{ index + 1 }}/{{ data.questionList.length }}
          <uni-icons type="down" size="12"></uni-icons>
        </view>
        <view class="criterionBtn" @click="next(false)">{{ index == data.questionList.length - 1 ? '提交' : '下一题' }}</view>
      </view>

      <view class="questionType" v-else>
        <view style="margin-bottom: 15rpx">错题情况</view>
        <view class="questionTypes">
          <view>题目总数【{{ data.totalQuestions }}】</view>
          <view>错误总数【{{ data.incorrectNumber }}】</view>
        </view>
        <view v-if="data.courseKnowledgeList && data.courseKnowledgeList.length > 0" style="margin-bottom: 15rpx; margin-top: 20rpx">知识点</view>
        <view class="know-container">
          <view class="know-item" v-for="(item, index) in data.courseKnowledgeList" :key="index">
            {{ item.knowledgeName }}
          </view>
        </view>
      </view>
    </view>
    <view v-if="status == 1 || status == 3">
      <SubjectTest ref="subject" :status="status" @addImgae="addimage" :item="data.questionList[index]" :index="index" @checked="subChecked"></SubjectTest>
    </view>
    <view v-else>
      <view class="tab-wrapper">
        <u-tabs :list="tabList" :current="current" lineColor="#007AFF" activeStyle="color: #000000; font-weight: bold;" inactiveStyle="color: #999999" @change="tabChange"></u-tabs>
      </view>
      <SubjectTest :item="e" :status="status" :index="i" v-for="(e, i) in data.questionList" @look="lookAnalysis" :key="e.id"></SubjectTest>
    </view>
    <u-popup :show="show" mode="bottom" @close="closePopup">
      <view class="questions">
        <view class="options" v-for="(e, i) in data.questionList" :key="e.id" @click="goIndex(i)">
          <view>{{ i + 1 }}.【{{ typeArr.find((o) => o.id == e.questionType).name }}】</view>
          <view>
            <span v-if="e.myAnswer" style="color: #428a6f">已填写</span>
            <span v-else style="color: #d85555">未填写</span>
          </view>
        </view>
      </view>
    </u-popup>

    <u-popup :show="popupVisible" mode="center" round="10" @close="closeLoadingPopup">
      <view class="popup-content">
        <view class="popup-title">提交批改中</view>
        <view class="custom-loading"></view>
        <u-button type="default" size="mini" @click="closeLoadingPopup" style="margin-top: 20rpx" class="criterionBtn">关闭</u-button>
      </view>
    </u-popup>

    <u-popup :show="teacherPopup" mode="center" round="10">
      <view class="popup-content">
        <view class="popup-title">助教强制收卷</view>
        <view class="custom-loading"></view>
      </view>
    </u-popup>
  </view>
  <!-- <view v-else>{{ info }}</view> -->
</template>

<script>
import { getTestPaper, sendTestPaper, getTestPaperPKResult, getToken, studentSubmit, getTestPaperWrongDetails } from '@/api/mathReq.js';
import SubjectTest from './components/subjectTest.vue';
export default {
  data() {
    return {
      popupVisible: false,
      show: false,
      rollShow: false,

      data: null,
      index: 0,
      status: 1, //1、3 开始答题 2查看解析
      time: 0,
      setIn: null,

      studentCode: '',
      studyId: '',
      classType: '',
      courseId: '',
      testPaperCategory: '', // 0-课上练习 1-课后练习 2-错题带刷 3-学前测试
      uuid: '',
      questionId: '', //错题带刷时需要

      testPaperId: '',

      tabList: [
        { name: '全部', type: 0 },
        { name: '只看错题', type: 1 }
      ],
      current: 0,

      typeArr: [
        { name: '单选题', id: 0 },
        { name: '填空题', id: 1 },
        { name: '计算题', id: 2 },
        { name: '解方程题', id: 3 },
        { name: '证明题', id: 4 },
        { name: '几何综合题', id: 5 }
      ],

      ///轮询pk结果
      isShowRefresh: false,
      pollCount: 0,
      maxPollCount: 3,
      pollTimer: null,
      isShowPK: false,
      isWin: false,
      stageNumber: 0,
      info: '',
      teacherPopup: false
    };
  },
  components: {
    SubjectTest
  },
  computed: {
    examTime() {
      return this.getFormotTime(this.time);
    }
  },
  onLoad(e) {
    console.log(JSON.stringify(e));
    this.info = e
    if (e.status) {
      this.status = e.status;
    }
    this.getStudentCode(e);
    this.uuid = e.uuid ?? '';
    this.studyId = e.studyId ?? '';
    this.classType = e.classType ?? '';
    this.courseId = e.courseId ?? '';
    this.testPaperCategory = e.testPaperCategory ?? '';
    this.questionId = e.questionId ?? '';
    this.stageNumber = e.stageNumber || 0;

    if (this.uuid) {
      uni.setStorageSync('token', this.uuid);
      // this.getToken();
    }
    if (this.testPaperCategory == 2) {
      this.getTestPaperWrong();
    } else {
      this.init();
    }
  },

  onShow() {
    let _this = this;
    window.callFromFlutter = function () {
      // if (msg == 'collectingPaper') {
      try {
        if (_this.setIn) clearInterval(_this.setIn);
        _this.status = 2;
        _this.teacherPopup = true;
        setTimeout(() => {
          _this.teacherPopup = false;
        }, 2000);
        let myAnswer = _this.data.questionList[_this.index].myAnswer;
        let arr = [];
        _this.data.questionList[_this.index].mathSmallQuestionList.forEach((e) => {
          if (e.myAnswerImage) {
            arr.push(e.myAnswerImage);
          } else {
            arr.push('');
          }
        });
        let sumbitObj = {
          testPaperId: _this.testPaperId,
          myAnswer: myAnswer,
          testPaperCategory: _this.testPaperCategory,
          studentCode: _this.studentCode,
          studyId: _this.studyId
        };
        if (_this.testPaperCategory == 2) {
          sumbitObj.questionId = _this.data.questionList[_this.index].id;
          sumbitObj.testPaperQuestionId = _this.data.questionList[_this.index].id;
        }
        const allEmpty = arr.every((item) => item == '');
        if (!allEmpty) {
          sumbitObj.myAnswerImage = arr.join(',');
        }
        _this.studentSubmit(sumbitObj).then((res) => {
          if (_this.testPaperCategory == 2) {
            _this.getTestPaperWrong();
          } else {
            _this.init();
          }
        });
      } catch (e) {
        if (_this.setIn) clearInterval(_this.setIn);
        _this.status = 2;
      }
    }
    // };
  },

  // === 生命周期钩子 ===
  mounted() {
    // 组件挂载后，开始监听来自父窗口的消息
    window.addEventListener('message', this.handleMessage);
    console.log('Uniapp 页面已挂载，并开始监听 message 事件。');
  },
  destroyed() {
    // 组件卸载时，移除监听，防止内存泄漏
    window.removeEventListener('message', this.handleMessage);
    console.log('Uniapp 页面已销毁，并移除 message 事件监听。');
  },
  methods: {


    /**
     * 这是暴露给外部调用的内部方法
     * @param {object} data 从项目A传来的数据
     */
    someInternalMethod(data) {
      console.log('项目A调用了我，传入了数据:', data);

      // 更新data，使其显示在模板上
      this.receivedMessage = JSON.stringify(data, null, 2);

      // 返回处理结果
      return { success: true, message: 'Uniapp操作成功' };
    },
    handleMessage(event) {
      // 安全性检查：可以检查 event.origin 来确保消息来源可靠
      // if (event.origin !== 'http://vue3-project-a.com') return;
      console.log('event.data')
      const { action, payload } = event.data;

      // 根据action调用不同的方法
      if (action === 'callMyMethod') {
        const result = this.someInternalMethod(payload);
        console.log('哈哈哈哈，‘123123')
        window.callFromFlutter('collectingPaper');
        // （可选）将执行结果回传给项目A
        event.source.postMessage({ action: 'methodResult', payload: result }, event.origin);
      }
    },
    getStudentCode(e) {

      if (e.studentCode) {
        var studentCodeList = e.studentCode.split(',');
        this.studentCode = studentCodeList[0];
      }
    },
    getFormotTime(time) {
      if (time == '') {
        return '00:00';
      }
      const sec = parseInt(time, 10);
      if (isNaN(sec)) {
        return '00:00';
      }
      const minutes = Math.floor(sec / 60);
      const remainingSeconds = sec % 60;

      // 格式化为两位数，不足补零
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(remainingSeconds).padStart(2, '0');
      return `${formattedMinutes}:${formattedSeconds}`;
    },
    tabChange(e) {
      this.current = e.index;
      this.status = 2;
      if (this.testPaperCategory == 2) {
        this.getTestPaperWrong();
      } else {
        this.init();
      }
    },
    closeLoadingPopup() {
      this.rollShow = false;
      this.popupVisible = false;
    },
    closePopup() {
      this.rollShow = false;
      this.show = false;
    },
    openOptions() {
      this.rollShow = true;
      this.show = true;
    },
    lookAnalysis(e) {
      this.rollShow = e;
    },

    ///获取完整token
    async getToken() {
      let data = await getToken({
        uuid: this.uuid
      });
      if (data) {
        console.log('getToken', data);
        await uni.setStorageSync('token', data);
        this.init();
      }
    },
    questionOptionFunc(data) {
      data.questionList.forEach((obj) => {
        ///将所有选项根据questionLocation算出是哪一小题
        obj.mathSmallQuestionList = [];
        const map = new Map();
        obj.mathQuestionOptionDtoList.forEach((item) => {
          const key = item.questionLocation;
          if (!map.has(key)) {
            map.set(key, {
              question: item.questionSmallProblem || '',
              optionList: []
            });
          }
          map.get(key).optionList.push(item);
        });
        obj.mathSmallQuestionList = Array.from(map.values());
        obj.isShowAnalysis = false;

        if (obj.answerList.length > 0) {
          //我的答案
          let myAnswerImage = obj.answerList[0].myAnswerImage.split(',');
          let myAnswer = obj.answerList[0].myAnswer.split(',');
          if (myAnswerImage.length != 0) {
            obj.mathSmallQuestionList.forEach((o, i) => {
              o.myAnswerImage = myAnswerImage[i];
            });
          }
          if (myAnswer.length != 0) {
            obj.mathSmallQuestionList.forEach((o, i) => {
              o.myAnswer = myAnswer[i];
              //回显答案
              if (obj.myAnswer.length == 0) {
                obj.myAnswer = myAnswer[i];
              } else {
                obj.myAnswer += ',' + myAnswer[i];
              }
            });
          }
        }
      });
    },
    //获取错题带刷
    async getTestPaperWrong() {
      let paramsReq = {
        studyId: this.studyId,
        classType: this.classType,
        studentCode: this.studentCode,
        questionId: this.questionId,
        wrongStatus: this.tabList[this.current].type
      };
      let data = await getTestPaperWrongDetails(paramsReq);
      if (data) {
        this.testPaperId = data.id;
        this.questionOptionFunc(data);
        this.data = data;
        // 0 未开始 1-答题中 2-已中止 3-已交卷
        if (data.examStatus == 2 || data.examStatus == 3 || data.examStatus == '') {
          this.status = 2;
        } else {
          this.status = 1;
        }
        if (this.status == 1 || this.status == 3) {
          this.time = this.data.examTime * 60;
          this.setIntiem();
        }
      }
    },
    ///获取试卷
    async init() {
      console.log(this.tabList[this.current]);
      let paramsReq = {
        studyId: this.studyId,
        classType: this.classType,
        courseId: this.courseId,
        studentCode: this.studentCode,
        stageNumber: this.stageNumber,
        testPaperCategory: this.testPaperCategory,
        questionId: this.questionId,
        wrongStatus: this.tabList[this.current].type
      };
      let data = await getTestPaper(paramsReq);
      if (data) {
        this.testPaperId = data.id;
        this.questionOptionFunc(data);
        this.data = data;
        // 0 未开始 1-答题中 2-已中止 3-已交卷
        if (data.examStatus == 2 || data.examStatus == 3) {
          this.status = 2;
        } else {
          this.status = 1;
        }
        if (this.status == 1 || this.status == 3) {
          this.time = this.data.examTime * 60;
          this.setIntiem();
        } else if (this.status == 2) {
          if (this.data.battleStatus == 1 && this.data.totalNumber > 1) {
            this.startPollingPKResult();
          }
        }
      }
    },
    last() {
      if (this.index <= 0) return;
      this.index--;
    },
    goIndex(e) {
      this.index = e;
      this.rollShow = false;
      this.show = false;
    },
    addimage(i, e) {
      console.log(i, e);
      this.data.questionList[this.index].mathSmallQuestionList[i].myAnswerImage = e;
      this.next(true);
    },
    subChecked(e) {
      this.data.questionList[this.index].myAnswer = e;
      this.next(true);
    },
    async next(isChoseRecord) {
      let _this = this;
      let arr = [];
      this.data.questionList[this.index].mathSmallQuestionList.forEach((e) => {
        if (e.myAnswerImage) {
          arr.push(e.myAnswerImage);
        } else {
          arr.push('');
        }
      });
      let myAnswer = this.data.questionList[this.index].myAnswer;
      let obj = {
        testPaperId: this.testPaperId,
        myAnswer: myAnswer,
        testPaperCategory: this.testPaperCategory,
        studentCode: this.studentCode,
        studyId: this.studyId,
        questionId: this.data.questionList[this.index].id,
        testPaperQuestionId: this.data.questionList[this.index].id
      };
      let sumbitObj = {
        testPaperId: this.testPaperId,
        myAnswer: myAnswer,
        testPaperCategory: this.testPaperCategory,
        studentCode: this.studentCode,
        studyId: this.studyId
      };
      if (this.testPaperCategory == 2) {
        sumbitObj.questionId = this.data.questionList[this.index].id;
        sumbitObj.testPaperQuestionId = this.data.questionList[this.index].id;
      }
      const allEmpty = arr.every((item) => item == '');
      if (!allEmpty) {
        obj.myAnswerImage = arr.join(',');
        sumbitObj.myAnswerImage = arr.join(',');
      }
      await sendTestPaper(obj);
      if (isChoseRecord) {
        //等true 代表保存答题记录
        return;
      }
      if (this.index == this.data.questionList.length - 1) {
        let content = '您已写完所有题目，提交后无法修改，确认是否提交';
        if (!this.getAllQuestionComplete()) {
          content = '您还未写完所有题目答案,是否提交';
        }
        uni.showModal({
          title: '提交答题卡',
          content: content,
          cancelText: '取消',
          confirmText: '提交',
          success: function (res) {
            if (res.confirm) {
              _this.popupVisible = true;
              _this.studentSubmit(sumbitObj);
            }
          }
        });
        return;
      }
      this.index++;
    },

    //判断所有题型已做
    getAllQuestionComplete() {
      let a = true;
      this.data.questionList.forEach((e) => {
        if (!e.myAnswer) {
          a = false;
        }
      });
      return a;
    },

    /// 学生交卷
    async studentSubmit(obj) {
      clearInterval(this.setIn);
      this.setIn = null;
      let data = await studentSubmit(obj);
      if (data) {
        this.status = 2;
        this.closeLoadingPopup();
        this.questionOptionFunc(data);
        this.data = data;
        //错题带刷无pk
        if (this.testPaperCategory != 2 && this.data.battleStatus == 1 && this.data.totalNumber > 1) {
          this.startPollingPKResult();
        }
      }
    },

    startPollingPKResult() {
      this.pollCount = 0;
      try {
        this.pollTimer = setInterval(() => {
          this.pollCount++;

          this.getPKResult()
            .then((res) => {
              //(0-不对战 1-对战)
              if (res && res.battleStatus == 1) {
                if (res && res.battleResults != null && res.battleResults != '') {
                  this.showPKUI(res);
                  clearInterval(this.pollTimer);
                  this.pollTimer = null;
                } else if (this.pollCount >= this.maxPollCount) {
                  clearInterval(this.pollTimer);
                  this.pollTimer = null;
                  console.warn('轮询三次后仍无结果，已取消轮询');
                  this.isShowRefresh = true;
                }
              } else {
                clearInterval(this.pollTimer);
                this.pollTimer = null;
                console.warn('不是PK模式');
              }
            })
            .catch((err) => {
              console.error('请求出错：', err);
              clearInterval(this.pollTimer);
              this.pollTimer = null;
              this.isShowRefresh = true;
            });
        }, 2000);
      } catch (e) {
        clearInterval(this.pollTimer);
        this.pollTimer = null;
        this.isShowRefresh = true;
      }
    },

    getPKResult() {
      let data = {
        studentCode: this.studentCode,
        testPaperId: this.testPaperId
      };
      return getTestPaperPKResult(data);
    },

    refreshBtn() {
      uni.showLoading({
        title: '正在加载',
        mask: true
      });
      this.getPKResult().then((res) => {
        uni.hideLoading();
        //(0-不对战 1-对战)
        if (res && res.battleStatus == 1 && this.data.totalNumber > 1) {
          if (res && res.battleResults != null && res.battleResults != '') {
            this.showPKUI(res);
          }
        } else {
          this.isShowRefresh = false;
        }
      });
    },

    showPKUI(res) {
      this.isShowRefresh = false;
      this.isShowPK = true;
      this.isWin = res.battleResults == 0 || res.battleResults == 1;
    },

    setIntiem() {
      this.setIn = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(this.setIn);
        } else {
          this.time--;
        }
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
.sumbit {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 328rpx;
  line-height: 80rpx;
  text-align: center;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 80rpx;
  color: #fff;
}
.questions {
  overflow-y: auto;
  height: 545rpx;
  padding: 43rpx 53rpx 0;
  .options {
    height: 45rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 52rpx;
    font-size: 32rpx;
  }
}
.start {
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
}
.pause {
  background: #fda324;
}
.criterionBtn {
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 60rpx;
  text-align: center;
  font-size: 28rpx;
  background-color: #428a6f;
  color: #fff;
  width: 180rpx;
}
.container {
  background-color: #f3f8fc;
  padding: 21rpx 24rpx;
  .line {
    height: 2rpx;
    width: 636rpx;
    margin: 20rpx auto;
    background-color: #dddddd;
  }
  .questionType {
    font-size: 30rpx;
    padding-left: 35rpx;
    padding-top: 28rpx;
    .questionTypes {
      display: flex;
      height: 40rpx;
      font-weight: bold;
      align-items: center;
      justify-content: space-between;
    }
  }
  .criterion {
    padding: 0 0 16rpx;
    border-radius: 20rpx;
    // height: 200rpx;
    background: #ffffff;
    .criterionBtns {
      display: flex;
      justify-content: space-between;
      padding: 0 10rpx;
      align-items: center;
      height: 80rpx;
      background-color: #fbfbfb;
    }
    .criterionTop {
      height: 118rpx;
      display: flex;
      align-items: center;
    }
    .criterionText {
      font-size: 30rpx;
      color: #333333;
    }
  }
  .testName {
    height: 100rpx;
    background: #ffffff;
    border-radius: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10rpx;
    margin-bottom: 16rpx;
  }
  .testNameText {
    width: 570px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 6rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.parentPKWinImg {
  width: 600rpx;
  height: 117rpx;
  background-image: url("https://document.dxznjy.com/dxSelect/pic_pkzuo.png");
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.parentPKFailImg {
  width: 600rpx;
  height: 117rpx;
  background-image: url("https://document.dxznjy.com/dxSelect/pic_shibaiyou.png");
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.avatar-wrapper {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}
.crown-icon {
  position: absolute;
  top: -20rpx;
  left: -10rpx;
  width: 60rpx;
  height: 60rpx;
  z-index: 10;
}
.avatar-win-box {
  width: 100%;
  height: 100%;
  background-image: url("https://document.dxznjy.com/dxSelect/pic_head_chengong.png");
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-fail-box {
  width: 100%;
  height: 100%;
  background-image: url("https://document.dxznjy.com/dxSelect/pic_head_shibai.png");
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-win-text {
  font-size: 35rpx;
  color: #fa9d54;
  font-weight: bold;
}
.avatar-fail-text {
  font-size: 35rpx;
  color: #9c9c9c;
  font-weight: bold;
}

.avatar-win-name {
  font-size: 28rpx;
  color: #fff;
  width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.avatar-fail-name {
  font-size: 28rpx;
  color: #777777;
  width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.know-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  padding: 10rpx;
}

.know-item {
  background-color: #f0f0f0;
  color: #333;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.tab-wrapper {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.popup-content {
  width: 400rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 10rpx;
}
.popup-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.custom-loading {
  width: 40px;
  height: 40px;
  border: 4px solid #ccc;
  border-top-color: #000;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin: 60rpx auto;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
