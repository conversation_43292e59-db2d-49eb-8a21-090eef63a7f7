#!/usr/bin/env node

/**
 * 项目启动脚本
 * 支持不同环境的快速启动
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logInfo(message) {
  log(`ℹ ${message}`, 'blue')
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green')
}

function logError(message) {
  log(`✗ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow')
}

// 显示帮助信息
function showHelp() {
  log('\n🚀 项目启动脚本', 'bright')
  log('=' .repeat(50))
  log('\n使用方法:')
  log('  node start.js [command] [options]', 'cyan')
  log('\n可用命令:')
  log('  dev          启动开发服务器 (默认)', 'cyan')
  log('  build        构建生产版本', 'cyan')
  log('  preview      预览构建结果', 'cyan')
  log('  lint         代码检查', 'cyan')
  log('  clean        清理构建文件', 'cyan')
  log('  help         显示帮助信息', 'cyan')
  log('\n选项:')
  log('  --port       指定端口号 (仅dev模式)', 'cyan')
  log('  --host       指定主机地址 (仅dev模式)', 'cyan')
  log('  --env        指定环境 (development|production)', 'cyan')
  log('\n示例:')
  log('  node start.js dev --port 3000', 'yellow')
  log('  node start.js build --env production', 'yellow')
  log('  node start.js preview', 'yellow')
  log('')
}

// 检查依赖是否安装
function checkDependencies() {
  if (!fs.existsSync('node_modules')) {
    logWarning('依赖未安装，正在安装...')
    return runCommand('npm', ['install'])
  }
  return true
}

// 运行命令
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    logInfo(`执行: ${command} ${args.join(' ')}`)
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    })
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(true)
      } else {
        reject(new Error(`命令执行失败，退出码: ${code}`))
      }
    })
    
    child.on('error', (error) => {
      reject(error)
    })
  })
}

// 清理构建文件
function cleanBuild() {
  const buildDir = path.join(__dirname, 'unpackage/dist')
  if (fs.existsSync(buildDir)) {
    logInfo('清理构建目录...')
    fs.rmSync(buildDir, { recursive: true, force: true })
    logSuccess('构建目录清理完成')
  } else {
    logInfo('构建目录不存在，无需清理')
  }
}

// 启动开发服务器
async function startDev(options = {}) {
  logInfo('启动开发服务器...')
  
  const args = ['run', 'serve']
  const env = { ...process.env }
  
  if (options.port) {
    env.VUE_APP_DEV_SERVER_PORT = options.port
  }
  
  if (options.host) {
    env.VUE_APP_DEV_SERVER_HOST = options.host
  }
  
  if (options.env) {
    env.NODE_ENV = options.env
  }
  
  await runCommand('npm', args, { env })
}

// 构建项目
async function buildProject(options = {}) {
  logInfo('构建生产版本...')
  
  const env = { ...process.env }
  if (options.env) {
    env.NODE_ENV = options.env
  }
  
  await runCommand('npm', ['run', 'build'], { env })
  logSuccess('构建完成！')
  
  // 显示构建结果
  const buildPath = path.join(__dirname, 'unpackage/dist/build/web')
  if (fs.existsSync(buildPath)) {
    logInfo(`构建输出目录: ${buildPath}`)
  }
}

// 预览构建结果
async function previewBuild() {
  const buildPath = path.join(__dirname, 'unpackage/dist/build/web')
  
  if (!fs.existsSync(buildPath)) {
    logError('构建文件不存在，请先执行构建')
    return
  }
  
  logInfo('启动预览服务器...')
  await runCommand('npm', ['run', 'preview'])
}

// 代码检查
async function lintCode() {
  logInfo('执行代码检查...')
  try {
    await runCommand('npm', ['run', 'lint'])
    logSuccess('代码检查通过')
  } catch (error) {
    logWarning('代码检查发现问题，请修复后重试')
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2)
  const command = args[0] || 'dev'
  const options = {}
  
  for (let i = 1; i < args.length; i++) {
    const arg = args[i]
    if (arg.startsWith('--')) {
      const key = arg.slice(2)
      const value = args[i + 1]
      if (value && !value.startsWith('--')) {
        options[key] = value
        i++
      } else {
        options[key] = true
      }
    }
  }
  
  return { command, options }
}

// 主函数
async function main() {
  const { command, options } = parseArgs()
  
  try {
    // 检查依赖
    if (!checkDependencies()) {
      return
    }
    
    switch (command) {
      case 'dev':
      case 'serve':
        await startDev(options)
        break
        
      case 'build':
        await buildProject(options)
        break
        
      case 'preview':
        await previewBuild()
        break
        
      case 'lint':
        await lintCode()
        break
        
      case 'clean':
        cleanBuild()
        break
        
      case 'help':
      case '--help':
      case '-h':
        showHelp()
        break
        
      default:
        logError(`未知命令: ${command}`)
        showHelp()
        process.exit(1)
    }
    
  } catch (error) {
    logError(`执行失败: ${error.message}`)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = {
  startDev,
  buildProject,
  previewBuild,
  lintCode,
  cleanBuild
}
