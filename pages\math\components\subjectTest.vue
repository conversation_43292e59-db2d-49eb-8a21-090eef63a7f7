<template>
  <view class="subjectTest" v-if="item && loading">
    <view class="title">
      {{ index + 1 }}.【{{ tyepName }}】
      <view style="">
        <view v-for="(segment, index) in parseContent(item.questionText)" :key="index" style="display: inline; vertical-align: baseline">
          <span v-if="segment.type === 'text'" style="display: inline; vertical-align: baseline">{{ segment.content }}</span>
          <span v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: baseline" v-html="option(segment.content)"></span>
        </view>
        ({{ item.questionScore }}分)
      </view>
    </view>
    <view class="imgs" v-if="questionImage.length != 0">
      <image v-for="(item, index) in questionImage" :src="item" mode="heightFix" :key="index" style="height: 170rpx" @click="previewImage(item)"></image>
    </view>
    <view class="questions" v-for="(obj, index) in item.mathSmallQuestionList" :key="index">
      <view class="questionsTitle" v-if="item.mathSmallQuestionList.length > 1">
        <view style="">
          {{ item.questionType == 1 ? tyepName + (index + 1) : '问' + (index + 1) }}:
          <view v-for="(segment, index) in parseContent(obj.question)" :key="index" style="display: inline; vertical-align: baseline">
            <span v-if="segment.type === 'text'" style="display: inline; vertical-align: baseline">{{ segment.content }}</span>
            <span v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: baseline" v-html="option(segment.content)"></span>
          </view>
        </view>
      </view>
      <view
        class="questionsItem"
        :class="answerList[index].answer == e.choiceOption ? answerList[index].class : ''"
        @click="check(e, index)"
        v-for="(e, i) in obj.optionList"
        :key="i"
      >
        <view style="line-height: 90rpx">
          <span style="display: inline; vertical-align: baseline; margin-right: 20rpx">{{ e.choiceOption }}.</span>
          <span v-for="(segment, index) in parseContent(e.content)" :key="index" style="display: inline; vertical-align: baseline">
            <span v-if="segment.type == 'text'" style="display: inline; vertical-align: baseline">{{ segment.content }}</span>
            <span v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: baseline" v-html="option(segment.content)"></span>
          </span>
        </view>
      </view>
      <view class="" v-if="photoTemp">
        <u-upload
          :fileList="answerList[index].fileList"
          @afterRead="(e) => afterRead(e, index)"
          @delete="(e) => deletePic(e, index)"
          multiple
          :capture="['camera']"
          width="150"
          height="150"
          :disabled="status == 2"
          :maxCount="1"
        ></u-upload>
      </view>
    </view>
    <view class="questionsTitle" v-if="status == 2" style="margin-top: 10rpx">
      正确答案：
      <span class="questionsRight">{{ item.correctAnswer }}</span>
    </view>
    <view v-if="status == 2" class="analysisParent">
      <view class="analysisTitle" @click="showAnalysis">
        <view style="color: #555555">本题解析</view>
        <uni-icons v-if="!item.isShowAnalysis" type="down" size="20"></uni-icons>
        <uni-icons v-if="item.isShowAnalysis" type="up" size="20"></uni-icons>
      </view>
      <view v-if="item.isShowAnalysis" style="padding: 30rpx 0rpx">
        <view class="imgs" v-if="item.analysisImgList && item.analysisImgList.length">
          <image v-for="(item, index) in item.analysisImgList" :src="item" :key="index" style="width: 200rpx; height: 200rpx" @click="previewImage(item)"></image>
        </view>
        <view style="">
          <view v-for="(segment, index) in parseContent(item.analysis)" :key="index" style="display: inline; vertical-align: baseline">
            <span v-if="segment.type === 'text'" style="display: inline; vertical-align: baseline">{{ segment.content }}</span>
            <span v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: baseline" v-html="option(segment.content)"></span>
          </view>
        </view>
      </view>
    </view>

    <view class="photo" v-if="photoTemp && status != 2">
      <view style="height: 42rpx; line-height: 42rpx; font-weight: bold; color: #000">拍照上传答案请注意以下几点哦</view>
      <view style="height: 42rpx; line-height: 42rpx">1.请保存卷面整洁</view>
      <view style="height: 42rpx; line-height: 42rpx">2.作答过程完整</view>
      <view style="height: 42rpx; line-height: 42rpx">3.单个题目的答案要在一张图片上</view>
      <view style="height: 42rpx; line-height: 42rpx">4.小题号书写规范，如(1) (2) (3)</view>
    </view>
  </view>
</template>

<script>
  import Config from '@/common/config.js';
  export default {
    props: {
      //题目
      item: {
        type: Object,
        default: () => {}
      },
      //索引
      index: {
        type: Number
      },
      //状态
      status: {
        type: Number,
        default: 0
      }
    },
    watch: {
      index: {
        handler(val) {
          this.questionImage = [];
          this.init();
        },
        immediate: true // 页面加载时立即执行一次
      }
    },
    data() {
      return {
        loading: false,
        fileList: [],
        latexHTML: '',
        typeArr: [
          { name: '单选题', id: 0 },
          { name: '填空题', id: 1 },
          { name: '计算题', id: 2 },
          { name: '解方程题', id: 3 },
          { name: '证明题', id: 4 },
          { name: '几何综合题', id: 5 }
        ],
        tyepName: '',
        photoTemp: false,
        answerList: [
          {
            fileList: []
          }
        ],
        questionImage: [] ///题目图片
      };
    },
    methods: {
      showAnalysis() {
        this.item.isShowAnalysis = !this.item.isShowAnalysis;
      },
      option(e) {
        return this.renderFormula(e);
      },
      check(e, i) {
        if (this.status == 2) return;
        this.answerList[i].answer = e.choiceOption;
        this.item.mathSmallQuestionList[i].myAnswer = e.choiceOption;
        this.$forceUpdate();
        let a = this.answerList.map((e) => e.answer).join(',');
        this.$emit('checked', a);
      },
      look() {
        this.$emit('look', true);
      },
      init() {
        if (this.item.questionImage.length != 0) {
          this.questionImage = this.item.questionImage.split(',');
        }

        this.tyepName = this.typeArr.find((e) => e.id == this.item.questionType).name;
        this.photoTemp = this.item.questionType != 0 && this.item.questionType != 1;
        this.latexHTML = this.renderFormula(this.item.questionText);

        this.answerList = [];
        this.item.mathSmallQuestionList.forEach((e, i) => {
          let obj = {
            index: i,
            answer: '',
            class: 'right',
            fileList: []
          };
          if (e.myAnswerImage) {
            obj.fileList[0] = { url: e.myAnswerImage };
          }
          if (e.myAnswer) {
            obj.answer = e.myAnswer;
            if (this.item.myAnswer.length == 0) {
              this.item.myAnswer = obj.answer;
            }
          }
          this.answerList.push(obj);
        });
        this.loading = true;
      },
      parseContent(e) {
        // 正则表达式匹配 \\(...\\)
        const regex = /\\\((.*?)\\\)/g;
        let lastIndex = 0;
        let match;
        const segments = [];

        // 循环匹配所有公式
        while ((match = regex.exec(e)) !== null) {
          // 添加前面的文本
          if (match.index > lastIndex) {
            segments.push({
              type: 'text',
              content: e.substring(lastIndex, match.index)
            });
          }

          // 添加公式
          segments.push({
            type: 'formula',
            content: match[1] // 获取括号内的内容
          });

          lastIndex = match.index + match[0].length;
        }

        // 添加剩余的文本
        if (lastIndex < e.length) {
          segments.push({
            type: 'text',
            content: e.substring(lastIndex)
          });
        }
        console.log(segments);
        return segments;
      },
      renderFormula(text) {
        try {
          if (!text) return;
          // let a = '关于方程\(3x^2 - 2x + 5 = 0\)，下列说法正确的是（ ），此方程是一元二次方程的原因是（ ）。';
          // console.log(text, a);
          // console.log(text);
          // text = '关于方程\(3x^2 - 2x + 5 = 0\)，下列说法正确的是（ ），此方程是一元二次方程的原因是（ ）。';
          // 转义百分号
          // return text;
          // 使用 KaTeX 渲染
          const rendered = this.$katex.renderToString(text, {
            throwOnError: false,
            displayMode: false,
            strict: false,
            trust: true
          });

          // 如果渲染失败，尝试直接返回原始表达式
          if (rendered.includes('ParseError')) {
            return text;
          }
          // console.log(rendered);
          return rendered;
        } catch (error) {
          console.error('Formula rendering error:', error);
          return text;
        }
      },
      previewImage(currentUrl) {
        // 如果要预览多张图片，可以传入 urls 数组
        uni.previewImage({
          current: currentUrl, // 当前显示图片的链接
          urls: [currentUrl] // 需要预览的图片链接列表
        });
      },
      deletePic(event, index) {
        this.answerList[index][`fileList${event.name}`].splice(event.index, 1);
        this.$emit('addImgae', index, '');
      },
      // 新增图片
      async afterRead(event, index) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        let fileListLen = this.answerList[index][`fileList${event.name}`].length;
        lists.map((item) => {
          this.answerList[index][`fileList${event.name}`].push({
            ...item,
            status: 'uploading',
            message: '上传中'
          });
        });
        for (let i = 0; i < lists.length; i++) {
          const result = await this.uploadFilePromise(lists[i].url);

          let item = this.answerList[index][`fileList${event.name}`][fileListLen];
          console.log(item);
          this.answerList[index][`fileList${event.name}`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: 'success',
              message: '',
              url: result.fileUrl
            })
          );
          fileListLen++;
        }
        this.$emit('addImgae', index, this.answerList[index].fileList[0].url);
      },
      uploadFilePromise(url) {
        let that = this;
        let arrimg = [];
        let baseURL = Config.IS_DEV ? Config.DEV_URL : Config.PRO_URL;
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: `${baseURL}zxAdminCourse/common/uploadFile`,
            filePath: url,
            name: 'file',
            formData: {
              user: 'test'
            },
            header: {
              Token: uni.getStorageSync('token')
            },
            success: (res) => {
              setTimeout(() => {
                let data = JSON.parse(res.data);
                resolve(data.data);
              }, 1000);
            }
          });
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .tools {
    margin-top: 20rpx;
    height: 69rpx;
    padding-right: 26rpx;
    display: flex;
    justify-content: flex-end;
  }
  .tool {
    width: 196rpx;
    height: 69rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    font-size: 32rpx;
    border-radius: 69rpx;
    color: #428a6f;
  }
  .study {
    border: 2rpx solid #428a6f;
  }
  .lookAnalysis {
    margin-left: 27rpx;
    background-color: #f7f7f7;
  }
  .right {
    border: 1rpx solid #94e6c7 !important;
    background: rgba(148, 230, 199, 0.15) !important;
    color: #31cf93 !important;
  }
  .subjectTest {
    border-radius: 10rpx;
    background-color: #fff;
    margin-top: 20rpx;
    padding: 30rpx 20rpx;
    .title {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15rpx;
    }
  }
  .imgs {
    margin-bottom: 15rpx;
    display: flex;
    padding: 24rpx;
    justify-content: space-around;
  }
  .photo {
    height: 271rpx;
    background-color: #f6f6f6;
    padding: 28rpx 0 0 56rpx;
    font-size: 26rpx;
    margin-top: 20rpx;
    color: #999;
  }
  .questions {
    .questionsItem {
      margin: 20rpx auto 18rpx;

      width: 650rpx;
      line-height: 90rpx;
      padding-left: 30rpx;
      box-sizing: border-box;
      border: 1rpx solid #dfdfdf;
      border-radius: 10rpx;
      font-size: 28rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      color: #333333;
    }
  }
  .questionsTitle {
    padding-left: 20rpx;
    min-height: 80rpx;
    line-height: 80rpx;
    background-color: #fff5ef;
    margin-top: 20rpx;
    // padding-left: 72rpx;
    color: #999;
  }
  .questionsRight {
    color: #009e74;
    font-weight: bold;
  }
  .analysisParent {
    background-color: #f6f7f9;
    // border: 1rpx solid #555555;
    margin-top: 20rpx;
    padding: 20rpx 30rpx;
  }
  .analysisTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
