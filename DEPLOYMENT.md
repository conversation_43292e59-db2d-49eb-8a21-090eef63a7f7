# 部署文档

## 项目概述

这是一个基于UniApp开发的企业微信H5项目，已配置为支持Node.js环境的构建和部署。

## 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.22.0
- Git

## 快速开始

### 1. 克隆项目

```bash
<NAME_EMAIL>:5ee323d293b16cdfea1276ae/dxqianduan/qyWechat-h5.git
cd qyWechat-h5
```

### 2. 安装依赖

```bash
npm install
# 或
yarn install
```

### 3. 环境配置

复制环境配置文件：

```bash
cp .env.example .env
```

根据实际情况修改 `.env` 文件中的配置。

### 4. 启动开发服务器

```bash
npm run dev
# 或使用启动脚本
node start.js dev
```

### 5. 构建生产版本

```bash
npm run build
# 或使用启动脚本
node start.js build
```

## 可用脚本

| 脚本 | 描述 |
|------|------|
| `npm run dev` | 启动开发服务器 |
| `npm run build` | 构建生产版本 |
| `npm run build:h5` | 构建H5版本 |
| `npm run build:h5:dev` | 构建H5开发版本 |
| `npm run build:h5:prod` | 构建H5生产版本 |
| `npm run serve` | 启动开发服务器 |
| `npm run preview` | 预览构建结果 |
| `npm run lint` | 代码检查 |
| `npm run lint:fix` | 自动修复代码问题 |

## 启动脚本使用

项目提供了便捷的启动脚本 `start.js`：

```bash
# 启动开发服务器
node start.js dev

# 指定端口启动
node start.js dev --port 3000

# 构建生产版本
node start.js build

# 预览构建结果
node start.js preview

# 代码检查
node start.js lint

# 清理构建文件
node start.js clean

# 查看帮助
node start.js help
```

## 环境配置

### 环境变量

项目支持以下环境变量：

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 环境模式 | development |
| `UNI_PLATFORM` | UniApp平台 | h5 |
| `VUE_APP_API_BASE_URL` | API基础URL | - |
| `VUE_APP_QY_CORPID` | 企业微信CorpID | - |
| `VUE_APP_QY_AGENTID` | 企业微信AgentID | - |
| `VUE_APP_QY_APPID` | 企业微信AppID | - |
| `VUE_APP_DEBUG` | 调试模式 | false |
| `VUE_APP_REQUEST_LOG` | 请求日志 | false |

### 环境配置文件

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `.env.example` - 配置文件模板

## Jenkins部署

### 1. Jenkins Pipeline

项目包含 `Jenkinsfile`，支持自动化构建：

```groovy
pipeline {
    agent any
    stages {
        stage('安装依赖') { ... }
        stage('代码检查') { ... }
        stage('构建项目') { ... }
        stage('构建验证') { ... }
        stage('打包构建产物') { ... }
    }
}
```

### 2. 构建产物

构建完成后，静态文件位于：
```
unpackage/dist/build/web/
├── index.html
├── static/
│   ├── css/
│   ├── js/
│   └── img/
└── assets/
```

### 3. 部署到Web服务器

将 `unpackage/dist/build/web/` 目录下的所有文件复制到Web服务器的根目录即可。

## Docker部署

### 1. 构建Docker镜像

```bash
docker build -t qywechat-h5 .
```

### 2. 运行容器

```bash
docker run -d --name qywechat-h5 -p 8080:80 qywechat-h5
```

### 3. 提取构建产物

```bash
# 从容器中复制构建产物
docker cp qywechat-h5:/app/unpackage/dist/build/web ./dist
```

## 常见问题

### 1. 依赖安装失败

```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 2. 构建失败

检查以下几点：
- Node.js版本是否符合要求
- 环境变量是否正确配置
- 依赖是否完整安装

### 3. 开发服务器无法访问

- 检查端口是否被占用
- 确认防火墙设置
- 检查host配置

### 4. 企业微信功能异常

- 确认企业微信配置是否正确
- 检查域名是否在企业微信后台配置
- 验证JS-SDK配置

## 项目结构

```
├── api/                    # API接口
├── common/                 # 公共文件
│   ├── config.js          # 配置文件
│   ├── utils.js           # 工具函数
│   └── ...
├── pages/                  # 页面文件
├── static/                 # 静态资源
├── store/                  # 状态管理
├── uni_modules/            # UniApp模块
├── .env.example           # 环境配置模板
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── .eslintrc.js           # ESLint配置
├── build.js               # 构建脚本
├── start.js               # 启动脚本
├── Dockerfile             # Docker配置
├── Jenkinsfile            # Jenkins Pipeline
├── package.json           # 项目配置
├── vue.config.js          # Vue配置
└── README.md              # 项目说明
```

## 技术栈

- **框架**: UniApp + Vue 2
- **UI库**: uView 2.0
- **构建工具**: Webpack
- **代码检查**: ESLint
- **包管理**: npm/yarn
- **容器化**: Docker
- **CI/CD**: Jenkins

## 联系方式

如有问题，请联系开发团队或查看项目文档。
