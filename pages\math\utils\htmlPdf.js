import html2pdf from 'html2pdf.js';

function htmlPdf(title, html) {
  const options = {
    margin: 10,
    filename: title + '.pdf',
    pagebreak: {
      mode: ['legacy', 'css'],
      before: '.pdf-split-page', 
      after: '.page-break-after',
      avoid: ['.page-break-avoid','.analysisParent']
      // avoid: 'p, .pdf-details, tr' // 扩展避免分页的元素列表
    },
    jsPDF: {
      unit: 'mm', // pt、mm、cm、in
      format: 'a4',
      // format: [210, 297],
      orientation: 'portrait' // 纵向p，横向l
    },
    html2canvas: {
      scale: 1,
      useCORS: true,
      logging: false,
      scrollX: 0,
      scrollY: 0
    }
  };

  html2pdf().set(options).from(html).save();
}
export default htmlPdf;
