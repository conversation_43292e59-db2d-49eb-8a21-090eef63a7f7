#!/usr/bin/env node

/**
 * 生产环境构建脚本
 * 用于Jenkins或其他CI/CD环境的自动化构建
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue')
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green')
}

function logError(message) {
  log(`✗ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow')
}

// 执行命令
function runCommand(command, description) {
  try {
    logStep('EXEC', `${description}: ${command}`)
    execSync(command, { stdio: 'inherit' })
    logSuccess(`${description} 完成`)
    return true
  } catch (error) {
    logError(`${description} 失败: ${error.message}`)
    return false
  }
}

// 检查文件是否存在
function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    logSuccess(`${description} 存在: ${filePath}`)
    return true
  } else {
    logWarning(`${description} 不存在: ${filePath}`)
    return false
  }
}

// 清理构建目录
function cleanBuildDir() {
  const buildDir = path.join(__dirname, 'unpackage/dist')
  if (fs.existsSync(buildDir)) {
    logStep('CLEAN', '清理构建目录')
    try {
      fs.rmSync(buildDir, { recursive: true, force: true })
      logSuccess('构建目录清理完成')
    } catch (error) {
      logWarning(`清理构建目录失败: ${error.message}`)
    }
  }
}

// 检查环境
function checkEnvironment() {
  logStep('CHECK', '检查构建环境')
  
  // 检查Node.js版本
  const nodeVersion = process.version
  log(`Node.js 版本: ${nodeVersion}`)
  
  // 检查npm版本
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
    log(`npm 版本: ${npmVersion}`)
  } catch (error) {
    logError('无法获取npm版本')
  }
  
  // 检查关键文件
  checkFile('package.json', 'package.json')
  checkFile('vue.config.js', 'Vue配置文件')
  checkFile('manifest.json', 'UniApp配置文件')
  checkFile('pages.json', '页面配置文件')
  checkFile('main.js', '入口文件')
}

// 安装依赖
function installDependencies() {
  logStep('INSTALL', '安装项目依赖')
  
  // 检查是否有yarn
  let hasYarn = false
  try {
    execSync('yarn --version', { stdio: 'ignore' })
    hasYarn = true
  } catch (error) {
    // yarn不存在，使用npm
  }
  
  const installCommand = hasYarn ? 'yarn install --frozen-lockfile' : 'npm ci'
  return runCommand(installCommand, '安装依赖')
}

// 构建项目
function buildProject() {
  logStep('BUILD', '构建生产版本')
  
  // 设置环境变量
  process.env.NODE_ENV = 'production'
  process.env.UNI_PLATFORM = 'h5'
  
  return runCommand('npm run build:h5:prod', '构建H5生产版本')
}

// 验证构建结果
function validateBuild() {
  logStep('VALIDATE', '验证构建结果')
  
  const buildPath = path.join(__dirname, 'unpackage/dist/build/web')
  const indexPath = path.join(buildPath, 'index.html')
  
  if (!checkFile(indexPath, '构建入口文件')) {
    return false
  }
  
  // 检查静态资源目录
  const staticPath = path.join(buildPath, 'static')
  if (!checkFile(staticPath, '静态资源目录')) {
    return false
  }
  
  // 获取构建文件大小
  try {
    const stats = fs.statSync(indexPath)
    log(`index.html 大小: ${(stats.size / 1024).toFixed(2)} KB`)
  } catch (error) {
    logWarning(`无法获取文件大小: ${error.message}`)
  }
  
  logSuccess('构建验证通过')
  return true
}

// 主构建流程
async function main() {
  log('🚀 开始生产环境构建', 'bright')
  log('=' .repeat(50))
  
  const startTime = Date.now()
  
  try {
    // 1. 检查环境
    checkEnvironment()
    
    // 2. 清理构建目录
    cleanBuildDir()
    
    // 3. 安装依赖
    if (!installDependencies()) {
      throw new Error('依赖安装失败')
    }
    
    // 4. 构建项目
    if (!buildProject()) {
      throw new Error('项目构建失败')
    }
    
    // 5. 验证构建结果
    if (!validateBuild()) {
      throw new Error('构建验证失败')
    }
    
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    log('=' .repeat(50))
    logSuccess(`🎉 构建完成! 耗时: ${duration}s`)
    log(`📦 构建输出目录: unpackage/dist/build/web`, 'blue')
    
    process.exit(0)
    
  } catch (error) {
    log('=' .repeat(50))
    logError(`❌ 构建失败: ${error.message}`)
    process.exit(1)
  }
}

// 运行构建
if (require.main === module) {
  main()
}

module.exports = {
  main,
  checkEnvironment,
  installDependencies,
  buildProject,
  validateBuild
}
