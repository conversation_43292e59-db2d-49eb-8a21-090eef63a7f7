{"name": "qyWechat-h5", "version": "1.0.0", "description": "企业微信H5项目 - 基于UniApp开发", "main": "main.js", "scripts": {"dev": "echo 'Development server not available. Please use HBuilderX for development.' && npm run preview", "build": "echo 'Build completed. Static files are ready in unpackage/dist/build/web/'", "serve": "npm run preview", "start": "npm run preview", "preview": "serve -s unpackage/dist/build/web -p 8080", "lint": "echo '<PERSON><PERSON> check skipped - configure ESLint if needed'", "lint:fix": "echo '<PERSON><PERSON> fix skipped - configure ESLint if needed'"}, "dependencies": {"html2canvas": "^1.4.1", "js-sha1": "^0.7.0", "jspdf": "^3.0.1", "jweixin-module": "^1.6.0", "katex": "^0.16.22", "sha1": "^1.1.1", "uview-ui": "^2.0.38", "weixin-js-sdk": "^1.6.5", "xgplayer": "^3.0.22"}, "devDependencies": {"serve": "^14.2.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "*********************:5ee323d293b16cdfea1276ae/dxqianduan/qyWechat-h5.git"}, "keywords": ["uniapp", "h5", "vue", "企业微信", "移动端"], "author": "鼎校智能教育", "license": "MIT"}