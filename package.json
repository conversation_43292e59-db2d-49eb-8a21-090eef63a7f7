{"name": "qyWechat-h5", "version": "1.0.0", "description": "企业微信H5项目 - 基于UniApp开发", "main": "main.js", "scripts": {"dev": "cross-env NODE_ENV=development uni build --watch", "build": "cross-env NODE_ENV=production uni build", "build:h5": "cross-env UNI_PLATFORM=h5 uni build", "build:h5:dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 uni build --watch", "build:h5:prod": "cross-env NODE_ENV=production UNI_PLATFORM=h5 uni build", "serve": "cross-env NODE_ENV=development UNI_PLATFORM=h5 uni serve", "serve:prod": "cross-env NODE_ENV=production UNI_PLATFORM=h5 uni serve", "start": "npm run serve", "preview": "serve -s unpackage/dist/build/web -l 8080", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix"}, "dependencies": {"html2canvas": "^1.4.1", "js-sha1": "^0.7.0", "jspdf": "^3.0.1", "jweixin-module": "^1.6.0", "katex": "^0.16.22", "sha1": "^1.1.1", "uview-ui": "^2.0.38", "weixin-js-sdk": "^1.6.5", "xgplayer": "^3.0.22"}, "devDependencies": {"@dcloudio/uni-cli": "^2.0.2", "@dcloudio/webpack-uni-mp-loader": "^2.0.2", "@dcloudio/webpack-uni-pages-loader": "^2.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.20.1", "serve": "^14.2.1", "webpack": "^4.46.0", "webpack-dev-server": "^3.11.3"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "*********************:5ee323d293b16cdfea1276ae/dxqianduan/qyWechat-h5.git"}, "keywords": ["uniapp", "h5", "vue", "企业微信", "移动端"], "author": "鼎校智能教育", "license": "MIT"}