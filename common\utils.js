export default {
  /**
   * 检查登录
   * @return {Boolean}
   */
  isLogin(options) {
    const token = uni.getStorageSync('token');
    if (token) {
      return true;
    }
    if (options != undefined && options != '') {
      //转到授权页面登录
      uni.switchTab({
        url: '/pages/user/user'
      })
    } else {
      return false;
    }
  },
  formatTime(time) {
    if (typeof time !== 'number' || time < 0) {
      return time
    }

    var hour = parseInt(time / 3600)
    time = time % 3600
    var minute = parseInt(time / 60)
    time = time % 60
    var second = time

    return ([hour, minute, second]).map(function(n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }).join(':')
  },

  /*
   * @description 生成时间戳
   */
  timestamp() {
    var date = new Date();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    var hours = date.getHours();
    var Minutes = date.getMinutes();
    var Seconds = date.getSeconds();
    if (month >= 1 && month <= 9) {
      month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = "0" + strDate;
    }
    if (hours >= 0 && hours <= 9) {
      hours = "0" + hours;
    }
    if (Minutes >= 0 && Minutes <= 9) {
      Minutes = "0" + Minutes;
    }
    if (Seconds >= 0 && Seconds <= 9) {
      Seconds = "0" + Seconds;
    }
    var times = date.getFullYear() + '' + month + '' + strDate + '' + hours + '' + Minutes + '' + Seconds;
    return times;
  },
  /*
   * @description 计算两个时间之间的时间差 多少天时分秒
   * @param endTime 结束时间
   */
  intervalTime(beginTime, endTime) {
    let start = beginTime;
    let end = endTime
    if (beginTime < endTime) {
      start = beginTime;
      end = endTime;
    } else {
      start = endTime;
      end = beginTime;
    }
    var date3 = (end - start); //时间差的毫秒数
    //计算出相差天数
    var days = Math.floor(date3 / (24 * 3600 * 1000));
    //计算出小时数
    var leave1 = date3 % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
    var hours = Math.floor(leave1 / (3600 * 1000));

    //计算分钟数
    var leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
    var minutes = Math.floor(leave2 / (60 * 1000));

    //计算秒数
    var leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
    var seconds = Math.round(leave3 / 1000);
    return (days ? (days + "天 ") : '') + (hours ? (hours + "小时 ") : '') + (minutes ? (minutes + "分钟 ") : '') + seconds +
      " 秒"
  },

  /*
   * @description 判读是否为外链
   * @param path
   * @returns {boolean}
   */
  isExternal(path) {
    return /^(https?:|mailto:|tel:)/.test(path);
  },
  /**
   * @description 校验密码是否小于6位
   * @param str
   * @returns {boolean}
   */
  isPassword(str) {
    return str.length >= 6;
  },

  /**
   * @description 判断是否为数字
   * @param value
   * @returns {boolean}
   */
  isNumber(value) {
    const reg = /^[0-9]*$/;
    return reg.test(value);
  },

  /**
   * @description 判断是否是小写字母
   * @param str
   * @returns {boolean}
   */
  isLowerCase(str) {
    const reg = /^[a-z]+$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否是大写字母
   * @param str
   * @returns {boolean}
   */
  isUpperCase(str) {
    const reg = /^[A-Z]+$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否是大写字母开头
   * @param str
   * @returns {boolean}
   */
  isAlphabets(str) {
    const reg = /^[A-Za-z]+$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否是字符串
   * @param str
   * @returns {boolean}
   */
  isString(str) {
    return typeof str === "string" || str instanceof String;
  },

  /**
   * @description 判断是否是数组
   * @param arg
   * @returns {arg is any[]|boolean}
   */
  isArray(arg) {
    if (typeof Array.isArray === "undefined") {
      return Object.prototype.toString.call(arg) === "[object Array]";
    }
    return Array.isArray(arg);
  },

  /**
   * @description 判断是否是手机号
   * @param str
   * @returns {boolean}
   */
  isPhone(str) {
    const reg = /^1\d{10}$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否是身份证号(第二代)
   * @param str
   * @returns {boolean}
   */
  isIdCard(str) {
    const reg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否是邮箱
   * @param str
   * @returns {boolean}
   */
  isEmail(str) {
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否中文
   * @param str
   * @returns {boolean}
   */
  isChina(str) {
    const reg = /^[\u4E00-\u9FA5]{2,4}$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否为空
   * @param str
   * @returns {boolean}
   */
  isNull(str) {
    return (
      str == null ||
      false ||
      str === "" ||
      str.trim() === "" ||
      str == undefined ||
      str.toLocaleLowerCase().trim() === "null"

    );
  },

  /**
   * @description 判断是否为固话
   * @param str
   * @returns {boolean}
   */
  isTel(str) {
    const reg = /^(400|800)([0-9\\-]{7,10})|(([0-9]{4}|[0-9]{3})(-| )?)?([0-9]{7,8})((-| |转)*([0-9]{1,4}))?$/;
    return reg.test(str);
  },

  /**
   * @description 判断是否为数字且最多两位小数
   * @param str
   * @returns {boolean}
   */
  isNum(str) {
    const reg = /^\d+(\.\d{1,2})?$/;
    return reg.test(str);
  },
  /**
   * 整数
   * @description 情况一：深度改变对象中的数字
   * <AUTHOR>
   * @param {Object} object 数字的父对象
   * @param {String} target 要监听的字段
   * @param {Number} value 数字
   * @description 情况二：改变的仅是data中的数字
   * @param {String} target 要监听的字段
   * @param {Number} value 数字
   * */
  checkInt(...arg) {
    const reg = /[^0-9$]/g;
    if (arg.length === 3) {
      let object = arg[0],
        target = arg[1],
        value = arg[2];
      let number = value.replace(reg, "");
      this.$set(object, target, number);
    } else {
      let target = arg[0],
        value = arg[1];
      let number = value.replace(reg, "");
      this.$data[target] = number;
    }
  },
  /**
   * 正整数
   * @description 情况一：深度改变对象中的数字
   * <AUTHOR>
   * @param {Object} object 数字的父对象
   * @param {String} target 要监听的字段
   * @param {Number} value 数字
   * @description 情况二：改变的仅是data中的数字
   * @param {String} target 要监听的字段
   * @param {Number} value 数字
   * */
  checkPositive(...arg) {
    if (arg.length === 3) {
      let object = arg[0],
        target = arg[1],
        value = arg[2];
      const flag = new RegExp("^[1-9][0-9]{0,}$").test(value);
      flag || this.$set(object, target, "");
    } else {
      let target = arg[0],
        value = arg[1];
      const flag = new RegExp("^[1-9][0-9]{0,}$").test(value);
      if (!flag) {
        this.$data[target] = "";
      }
    }
  },
  /**
   * 限制数字输入小数点后几位
   * @description 情况一：深度改变对象中的数字
   * <AUTHOR>
   * @param {Number} limit 允许输入小数点后几位 例：1代表允许输入小数点后1位
   * @param {Number} nums input框输入的值
   * @param {Number} object 数字的父对象
   * @param {String} target 要监听的字段
   * @description 情况二：改变的仅是data中的数字
   * @param {Number} limit 允许输入小数点后几位 例：1代表允许输入小数点后1位
   * @param {Number} nums input框输入的值
   * @param {String} target 要监听的字段
   * */
  checkFloat(...arg) {
    const onlyDecimal = (num, limit) => {
      let number = null;
      if (num.indexOf(".") != -1) {
        var str_ = num.substr(num.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          number = num.substr(0, num.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      let array = num.split(".");
      if (!!array[1] && array[1].length > limit) {
        array[1] = array[1].substr(0, limit);
        number = array[0] + "." + array[1];
      }
      return number;
    };

    let limit = arg[0];
    let nums = arg[1];
    if (nums) {
      nums = nums.replace(/[^0-9.]/g, "");
      if (onlyDecimal(nums, limit) != null) {
        nums = onlyDecimal(nums, limit);
      }
    }
    if (arg.length === 4) {
      let object = arg[2];
      let target = arg[3];
      this.$set(object, target, nums);
    } else {
      let target = arg[2];
      this.$data[target] = nums;
    }
  },
  /**
   * @description 计算小数点后面有几位数
   * <AUTHOR>
   * @param {Number} n 要验证的数字
   *
   */
  countFloat(n) {
    try {
      return n.toString().split(".")[1].length;
    } catch (err) {
      return 0;
    }
  },
  /* 
   *val为传入的地址，此地址的校验是地图中能选取到的地址，对于个人的叫法地址请谨慎使用
   * @description 去除详细地址中的省市
   */
  addrName(val) {
    var reg =
      /([^省]+自治区|.*?省|.*?行政区|.*?市)([^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)([^县]+县|.+[^社]区|.+市|.+旗|.+海域|.+岛)/;
    var reg1 = /(.市)([^县]+县|.+[^社]区|.+市|.+旗|.+海域|.+岛)/; //此正则处理有直辖市和地级市同时存在的情况
    var result = reg.exec(val);
    var result1 = reg1.exec(val);
    if (result) {
      return val.split(result[0])[1];
    } else if (result1) {
      return val.split(result1[0])[1];
    } else {
      //对于没有省市区的地址处理返回空
      return val;
    }
  },
}