//如果用uni请求
import http from '@/common/uni.interface.js';
//如果是用uview请求,就用下面这句
//const http = uni.$u.http
//获取视频列表
export function getVideoList(params) {
  return http.get('zx/wap/course/study/video/list', params)
}

// 查询保利威视频信息
export function getVideoInfo(params, isLoading) {
  return http.get('znyy/pd/pdCourse/getVideoInfo', params, {
    custom: {
      ShowLoading: isLoading
    }
  })
}

// 学习记录查询
export function getStudyRecord(params) {
  return http.get('zx/wap/course/get/learn/record', params)
}

// 学习记录保存
export function saveStudyRecord(params) {
  return http.get('zx/wap/course/data/learn/record/save', params)
}

// 获取视频节点信息
export function getVideoNode(params, isLoading) {
  return http.get('dyf/web/xktVideo/get-nodes', params, {
    custom: {
      ShowLoading: isLoading
    }
  })
}