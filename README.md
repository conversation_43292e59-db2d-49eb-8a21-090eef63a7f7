# qy-h5

#### 介绍
该项目为企业微信中通过链接跳转小程序的一个中间页以及一些其他的h5页面

#### 软件架构
uniapp+uView2.0

###	项目结构
```
├─api				// 接口管理
├─common			// 公共文件
│  ├─config 			// 环境配置 开发、生产环境地址和企微相关配置
│  ├─filters 			// 全局过滤器
│  ├─uni.interface 		// uniapp请求模式下的请求及响应处理；
│  ├─uview.interface 		// vuiew请求模式下的请求及响应处理(uView2.0)
│  └─utils 			// 常用公共方法
├─uni_modules			// 插件市场插件目录
│  └─uview-ui			// uview-ui	
├─pages			// 插件市场插件目录
│  └─index  跳转助教端接单首页
│  ├─mettingDetail  会议详情h5页面
│  ├─playerVod   保利威播放视频h5页面
│  ├─qrCode      添加助教二维码页面
│  ├─selectRole  助教端选择身份h5页面
│  ├─takingOrder 跳转助教端接单详情页
```

###  使用方法

```
本地环境中  将config.js中的相关配置按注释更改为本地配置 再根据后端发送的接单消息链接配置相关ngrok启动 
线上环境   一般情况下不需要重新配置打包  如有更改一定要在config.js中将相关配置切换为线上环境

#### 仓库地址

[codeup](*********************:5ee323d293b16cdfea1276ae/dxqianduan/qyWechat-h5.git)

