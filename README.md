# qy-h5

#### 介绍

该项目为企业微信中通过链接跳转小程序的一个中间页以及一些其他的 h5 页面

#### 软件架构

uniapp+uView2.0

### 项目结构

```
├─api				// 接口管理
├─common			// 公共文件
│  ├─config 			// 环境配置 开发、生产环境地址和企微相关配置
│  ├─filters 			// 全局过滤器
│  ├─uni.interface 		// uniapp请求模式下的请求及响应处理；
│  ├─uview.interface 		// vuiew请求模式下的请求及响应处理(uView2.0)
│  └─utils 			// 常用公共方法
├─uni_modules			// 插件市场插件目录
│  └─uview-ui			// uview-ui
├─pages			// 插件市场插件目录
│  └─index  跳转助教端接单首页
│  ├─mettingDetail  会议详情h5页面
│  ├─playerVod   保利威播放视频h5页面
│  ├─qrCode      添加助教二维码页面
│  ├─selectRole  助教端选择身份h5页面
│  ├─takingOrder 跳转助教端接单详情页
```

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.22.0

### 快速开始

#### 1. 安装依赖

```bash
npm install
# 或
yarn install
```

#### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 根据实际情况修改 .env 文件中的配置
```

#### 3. 启动开发服务器

```bash
npm run dev
# 或使用启动脚本
node start.js dev
```

#### 4. 构建生产版本

```bash
npm run build
# 或使用启动脚本
node start.js build
```

### 可用脚本

| 脚本              | 描述           |
| ----------------- | -------------- |
| `npm run dev`     | 启动开发服务器 |
| `npm run build`   | 构建生产版本   |
| `npm run serve`   | 启动开发服务器 |
| `npm run preview` | 预览构建结果   |
| `npm run lint`    | 代码检查       |

### 启动脚本

项目提供了便捷的启动脚本 `start.js`：

```bash
# 启动开发服务器
node start.js dev

# 构建生产版本
node start.js build

# 预览构建结果
node start.js preview

# 查看帮助
node start.js help
```

### Jenkins 部署

项目已配置 Jenkins Pipeline 支持，包含：

- 自动依赖安装
- 代码质量检查
- 生产环境构建
- 构建产物归档

构建产物位于：`unpackage/dist/build/web/`

### 环境配置说明

项目支持环境变量配置，主要配置项：

- **API 地址**: `VUE_APP_API_BASE_URL`
- **企业微信配置**: `VUE_APP_QY_CORPID`、`VUE_APP_QY_AGENTID`、`VUE_APP_QY_APPID`
- **调试模式**: `VUE_APP_DEBUG`

详细配置请参考 `.env.example` 文件。

### 使用方法

```
开发环境：
1. 复制 .env.example 为 .env
2. 修改环境配置文件中的相关参数
3. 运行 npm run dev 启动开发服务器

生产环境：
1. 确保 .env.production 配置正确
2. 运行 npm run build 构建生产版本
3. 将 unpackage/dist/build/web 目录部署到Web服务器
```

#### 仓库地址

[codeup](*********************:5ee323d293b16cdfea1276ae/dxqianduan/qyWechat-h5.git)
