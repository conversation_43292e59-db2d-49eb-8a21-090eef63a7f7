<template>
  <view>
    <view class="container t-c flex-col bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <image :src="qrCode" class="mb-20 img" :show-menu-by-longpress="true" mode=""></image>
      <view class="title">长按二维码添加教练</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ifBottomRefresh: false,
      qrCode: '',
      useHeight: 0
    };
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 30;
      }
    });
  },
  onLoad(e) {
    if (e.code) {
      // this.qrCode = e.qrCode;
      this.qrCode = JSON.parse(decodeURIComponent(e.code));
    }
  },
  onShow() {},
  methods: {
    saveCode(url) {
      uni.showModal({
        content: '保存二维码？',
        success: (res) => {
          if (res.confirm) {
            uni.downloadFile({
              url,
              success: (res) => {
                console.log(res.tempFilePath);
                // 获取到图片本地地址后再保存图片到相册(因为此方法不支持远程地址)
                uni.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    uni.showToast({
                      title: '保存成功！'
                    });
                  },
                  fail: (err) => {
                    uni.showToast({
                      title: '保存失败！',
                      icon: 'none'
                    });
                  }
                });
              }
            });
          } else {
            uni.showToast({
              title: '已取消！',
              icon: 'none'
            });
          }
        }
      });
    },
    goBack() {
      uni.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #fff;
  border-radius: 15rpx;
  margin: 0 30rpx;
}
.img {
  width: 500rpx;
  height: 500rpx;
}
.tips-title {
  color: #2e896f;
  font-weight: 600;
}
.title {
  color: #666666;
  font-size: 28rpx;
  padding: 0 10rpx;
}
</style>
